---
- node:
    id: node1

- tcp-listener:
    port: 11111

- control-service:
    filename: /tmp/receptorctltest/mesh1/node1.sock

- tcp-server:
    port: 11112
    remotenode: localhost
    remoteservice: control

- tls-server:
    name: tlsserver
    key: /tmp/receptorctltest/mesh1/server.key
    cert: /tmp/receptorctltest/mesh1/server.crt
    requireclientcert: true
    clientcas: /tmp/receptorctltest/mesh1/ca.crt

- control-service:
    service: ctltls
    tcplisten: 11113
    tcptls: tlsserver
...
