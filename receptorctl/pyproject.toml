[project]
name = "receptorctl"
authors = [{name = "Red Hat", email = "<EMAIL>"}]
description = "Receptorctl is a front-end CLI and importable Python library that interacts with Re<PERSON> over its control socket interface."
readme = "README.md"
dynamic = ["version"]
dependencies = [
  "python-dateutil>=2.8.1",
  "click>=8.1.3, <8.2.0",
  "PyYAML>=5.4.1",
]

[project.license]
text = "Apache-2.0"

[project.urls]
Homepage = "https://ansible.readthedocs.io/projects/receptor/"
Documentation = "https://ansible.readthedocs.io/projects/receptor/en/latest/"
Repository = "https://github.com/ansible/receptor"
Issues = "https://github.com/ansible/receptor/issues"

[build-system]
requires = ["setuptools>=75.3.2", "setuptools-scm>=7.1.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools_scm]
fallback_version = "0.0.0"

[project.optional-dependencies]
test = [
  "coverage",
  "pip-tools>=7",
  "pytest",
  "pytest-cov",
  "ruff",
]

[project.scripts]
receptorctl = "receptorctl:run"

[tool.ruff]
line-length = 100

[tool.pip-tools]
resolver = "backtracking"
allow-unsafe = true
strip-extras = true
quiet = true

[tool.coverage.run]
omit = ["tests/*"]