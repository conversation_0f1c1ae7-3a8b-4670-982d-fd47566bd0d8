// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/backends/websockets.go
//
// Generated by this command:
//
//	mockgen -source=pkg/backends/websockets.go -destination=pkg/backends/mock_backends/websockets.go
//

// Package mock_backends is a generated GoMock package.
package mock_backends

import (
	context "context"
	tls "crypto/tls"
	net "net"
	http "net/http"
	reflect "reflect"
	sync "sync"

	backends "github.com/ansible/receptor/pkg/backends"
	netceptor "github.com/ansible/receptor/pkg/netceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockGorillaWebsocketDialerForDialer is a mock of GorillaWebsocketDialerForDialer interface.
type MockGorillaWebsocketDialerForDialer struct {
	ctrl     *gomock.Controller
	recorder *MockGorillaWebsocketDialerForDialerMockRecorder
	isgomock struct{}
}

// MockGorillaWebsocketDialerForDialerMockRecorder is the mock recorder for MockGorillaWebsocketDialerForDialer.
type MockGorillaWebsocketDialerForDialerMockRecorder struct {
	mock *MockGorillaWebsocketDialerForDialer
}

// NewMockGorillaWebsocketDialerForDialer creates a new mock instance.
func NewMockGorillaWebsocketDialerForDialer(ctrl *gomock.Controller) *MockGorillaWebsocketDialerForDialer {
	mock := &MockGorillaWebsocketDialerForDialer{ctrl: ctrl}
	mock.recorder = &MockGorillaWebsocketDialerForDialerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGorillaWebsocketDialerForDialer) EXPECT() *MockGorillaWebsocketDialerForDialerMockRecorder {
	return m.recorder
}

// DialContext mocks base method.
func (m *MockGorillaWebsocketDialerForDialer) DialContext(ctx context.Context, urlStr string, requestHeader http.Header) (backends.Conner, *http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DialContext", ctx, urlStr, requestHeader)
	ret0, _ := ret[0].(backends.Conner)
	ret1, _ := ret[1].(*http.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// DialContext indicates an expected call of DialContext.
func (mr *MockGorillaWebsocketDialerForDialerMockRecorder) DialContext(ctx, urlStr, requestHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DialContext", reflect.TypeOf((*MockGorillaWebsocketDialerForDialer)(nil).DialContext), ctx, urlStr, requestHeader)
}

// MockWebsocketListenerForWebsocket is a mock of WebsocketListenerForWebsocket interface.
type MockWebsocketListenerForWebsocket struct {
	ctrl     *gomock.Controller
	recorder *MockWebsocketListenerForWebsocketMockRecorder
	isgomock struct{}
}

// MockWebsocketListenerForWebsocketMockRecorder is the mock recorder for MockWebsocketListenerForWebsocket.
type MockWebsocketListenerForWebsocketMockRecorder struct {
	mock *MockWebsocketListenerForWebsocket
}

// NewMockWebsocketListenerForWebsocket creates a new mock instance.
func NewMockWebsocketListenerForWebsocket(ctrl *gomock.Controller) *MockWebsocketListenerForWebsocket {
	mock := &MockWebsocketListenerForWebsocket{ctrl: ctrl}
	mock.recorder = &MockWebsocketListenerForWebsocketMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebsocketListenerForWebsocket) EXPECT() *MockWebsocketListenerForWebsocketMockRecorder {
	return m.recorder
}

// Addr mocks base method.
func (m *MockWebsocketListenerForWebsocket) Addr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Addr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// Addr indicates an expected call of Addr.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) Addr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Addr", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).Addr))
}

// GetAddr mocks base method.
func (m *MockWebsocketListenerForWebsocket) GetAddr() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddr")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAddr indicates an expected call of GetAddr.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) GetAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddr", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).GetAddr))
}

// GetTLS mocks base method.
func (m *MockWebsocketListenerForWebsocket) GetTLS() *tls.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTLS")
	ret0, _ := ret[0].(*tls.Config)
	return ret0
}

// GetTLS indicates an expected call of GetTLS.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) GetTLS() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTLS", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).GetTLS))
}

// Path mocks base method.
func (m *MockWebsocketListenerForWebsocket) Path() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Path")
	ret0, _ := ret[0].(string)
	return ret0
}

// Path indicates an expected call of Path.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) Path() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Path", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).Path))
}

// SetPath mocks base method.
func (m *MockWebsocketListenerForWebsocket) SetPath(path string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetPath", path)
}

// SetPath indicates an expected call of SetPath.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) SetPath(path any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPath", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).SetPath), path)
}

// Start mocks base method.
func (m *MockWebsocketListenerForWebsocket) Start(ctx context.Context, wg *sync.WaitGroup) (chan netceptor.BackendSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx, wg)
	ret0, _ := ret[0].(chan netceptor.BackendSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Start indicates an expected call of Start.
func (mr *MockWebsocketListenerForWebsocketMockRecorder) Start(ctx, wg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockWebsocketListenerForWebsocket)(nil).Start), ctx, wg)
}

// MockGorillaWebsocketUpgraderForListener is a mock of GorillaWebsocketUpgraderForListener interface.
type MockGorillaWebsocketUpgraderForListener struct {
	ctrl     *gomock.Controller
	recorder *MockGorillaWebsocketUpgraderForListenerMockRecorder
	isgomock struct{}
}

// MockGorillaWebsocketUpgraderForListenerMockRecorder is the mock recorder for MockGorillaWebsocketUpgraderForListener.
type MockGorillaWebsocketUpgraderForListenerMockRecorder struct {
	mock *MockGorillaWebsocketUpgraderForListener
}

// NewMockGorillaWebsocketUpgraderForListener creates a new mock instance.
func NewMockGorillaWebsocketUpgraderForListener(ctrl *gomock.Controller) *MockGorillaWebsocketUpgraderForListener {
	mock := &MockGorillaWebsocketUpgraderForListener{ctrl: ctrl}
	mock.recorder = &MockGorillaWebsocketUpgraderForListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGorillaWebsocketUpgraderForListener) EXPECT() *MockGorillaWebsocketUpgraderForListenerMockRecorder {
	return m.recorder
}

// Upgrade mocks base method.
func (m *MockGorillaWebsocketUpgraderForListener) Upgrade(w http.ResponseWriter, r *http.Request, responseHeader http.Header) (backends.Conner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upgrade", w, r, responseHeader)
	ret0, _ := ret[0].(backends.Conner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upgrade indicates an expected call of Upgrade.
func (mr *MockGorillaWebsocketUpgraderForListenerMockRecorder) Upgrade(w, r, responseHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upgrade", reflect.TypeOf((*MockGorillaWebsocketUpgraderForListener)(nil).Upgrade), w, r, responseHeader)
}

// MockHTTPServerForListener is a mock of HTTPServerForListener interface.
type MockHTTPServerForListener struct {
	ctrl     *gomock.Controller
	recorder *MockHTTPServerForListenerMockRecorder
	isgomock struct{}
}

// MockHTTPServerForListenerMockRecorder is the mock recorder for MockHTTPServerForListener.
type MockHTTPServerForListenerMockRecorder struct {
	mock *MockHTTPServerForListener
}

// NewMockHTTPServerForListener creates a new mock instance.
func NewMockHTTPServerForListener(ctrl *gomock.Controller) *MockHTTPServerForListener {
	mock := &MockHTTPServerForListener{ctrl: ctrl}
	mock.recorder = &MockHTTPServerForListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHTTPServerForListener) EXPECT() *MockHTTPServerForListenerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockHTTPServerForListener) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockHTTPServerForListenerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockHTTPServerForListener)(nil).Close))
}

// Serve mocks base method.
func (m *MockHTTPServerForListener) Serve(l net.Listener) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Serve", l)
	ret0, _ := ret[0].(error)
	return ret0
}

// Serve indicates an expected call of Serve.
func (mr *MockHTTPServerForListenerMockRecorder) Serve(l any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Serve", reflect.TypeOf((*MockHTTPServerForListener)(nil).Serve), l)
}

// ServeTLS mocks base method.
func (m *MockHTTPServerForListener) ServeTLS(l net.Listener, certFile, keyFile string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServeTLS", l, certFile, keyFile)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServeTLS indicates an expected call of ServeTLS.
func (mr *MockHTTPServerForListenerMockRecorder) ServeTLS(l, certFile, keyFile any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServeTLS", reflect.TypeOf((*MockHTTPServerForListener)(nil).ServeTLS), l, certFile, keyFile)
}

// SetHandeler mocks base method.
func (m *MockHTTPServerForListener) SetHandeler(mux *http.ServeMux) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetHandeler", mux)
}

// SetHandeler indicates an expected call of SetHandeler.
func (mr *MockHTTPServerForListenerMockRecorder) SetHandeler(mux any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHandeler", reflect.TypeOf((*MockHTTPServerForListener)(nil).SetHandeler), mux)
}

// SetTLSConfig mocks base method.
func (m *MockHTTPServerForListener) SetTLSConfig(tlscfg *tls.Config) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTLSConfig", tlscfg)
}

// SetTLSConfig indicates an expected call of SetTLSConfig.
func (mr *MockHTTPServerForListenerMockRecorder) SetTLSConfig(tlscfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTLSConfig", reflect.TypeOf((*MockHTTPServerForListener)(nil).SetTLSConfig), tlscfg)
}

// MockConner is a mock of Conner interface.
type MockConner struct {
	ctrl     *gomock.Controller
	recorder *MockConnerMockRecorder
	isgomock struct{}
}

// MockConnerMockRecorder is the mock recorder for MockConner.
type MockConnerMockRecorder struct {
	mock *MockConner
}

// NewMockConner creates a new mock instance.
func NewMockConner(ctrl *gomock.Controller) *MockConner {
	mock := &MockConner{ctrl: ctrl}
	mock.recorder = &MockConnerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConner) EXPECT() *MockConnerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockConner) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockConnerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockConner)(nil).Close))
}

// ReadMessage mocks base method.
func (m *MockConner) ReadMessage() (int, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadMessage")
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadMessage indicates an expected call of ReadMessage.
func (mr *MockConnerMockRecorder) ReadMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadMessage", reflect.TypeOf((*MockConner)(nil).ReadMessage))
}

// WriteMessage mocks base method.
func (m *MockConner) WriteMessage(messageType int, data []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMessage", messageType, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteMessage indicates an expected call of WriteMessage.
func (mr *MockConnerMockRecorder) WriteMessage(messageType, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMessage", reflect.TypeOf((*MockConner)(nil).WriteMessage), messageType, data)
}
