// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/utils/net.go
//
// Generated by this command:
//
//	mockgen -source=pkg/utils/net.go -destination=pkg/utils/mock_utils/net.go
//

// Package mock_utils is a generated GoMock package.
package mock_utils

import (
	net "net"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockNetListener is a mock of NetListener interface.
type MockNetListener struct {
	ctrl     *gomock.Controller
	recorder *MockNetListenerMockRecorder
	isgomock struct{}
}

// MockNetListenerMockRecorder is the mock recorder for MockNetListener.
type MockNetListenerMockRecorder struct {
	mock *MockNetListener
}

// NewMockNetListener creates a new mock instance.
func NewMockNetListener(ctrl *gomock.Controller) *MockNetListener {
	mock := &MockNetListener{ctrl: ctrl}
	mock.recorder = &MockNetListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetListener) EXPECT() *MockNetListenerMockRecorder {
	return m.recorder
}

// Accept mocks base method.
func (m *MockNetListener) Accept() (net.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Accept")
	ret0, _ := ret[0].(net.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Accept indicates an expected call of Accept.
func (mr *MockNetListenerMockRecorder) Accept() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Accept", reflect.TypeOf((*MockNetListener)(nil).Accept))
}

// Addr mocks base method.
func (m *MockNetListener) Addr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Addr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// Addr indicates an expected call of Addr.
func (mr *MockNetListenerMockRecorder) Addr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Addr", reflect.TypeOf((*MockNetListener)(nil).Addr))
}

// Close mocks base method.
func (m *MockNetListener) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockNetListenerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockNetListener)(nil).Close))
}

// MockNetConn is a mock of NetConn interface.
type MockNetConn struct {
	ctrl     *gomock.Controller
	recorder *MockNetConnMockRecorder
	isgomock struct{}
}

// MockNetConnMockRecorder is the mock recorder for MockNetConn.
type MockNetConnMockRecorder struct {
	mock *MockNetConn
}

// NewMockNetConn creates a new mock instance.
func NewMockNetConn(ctrl *gomock.Controller) *MockNetConn {
	mock := &MockNetConn{ctrl: ctrl}
	mock.recorder = &MockNetConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetConn) EXPECT() *MockNetConnMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockNetConn) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockNetConnMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockNetConn)(nil).Close))
}

// LocalAddr mocks base method.
func (m *MockNetConn) LocalAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocalAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// LocalAddr indicates an expected call of LocalAddr.
func (mr *MockNetConnMockRecorder) LocalAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocalAddr", reflect.TypeOf((*MockNetConn)(nil).LocalAddr))
}

// Read mocks base method.
func (m *MockNetConn) Read(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read.
func (mr *MockNetConnMockRecorder) Read(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockNetConn)(nil).Read), b)
}

// RemoteAddr mocks base method.
func (m *MockNetConn) RemoteAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoteAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// RemoteAddr indicates an expected call of RemoteAddr.
func (mr *MockNetConnMockRecorder) RemoteAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoteAddr", reflect.TypeOf((*MockNetConn)(nil).RemoteAddr))
}

// SetDeadline mocks base method.
func (m *MockNetConn) SetDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeadline indicates an expected call of SetDeadline.
func (mr *MockNetConnMockRecorder) SetDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeadline", reflect.TypeOf((*MockNetConn)(nil).SetDeadline), t)
}

// SetReadDeadline mocks base method.
func (m *MockNetConn) SetReadDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadDeadline indicates an expected call of SetReadDeadline.
func (mr *MockNetConnMockRecorder) SetReadDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadDeadline", reflect.TypeOf((*MockNetConn)(nil).SetReadDeadline), t)
}

// SetWriteDeadline mocks base method.
func (m *MockNetConn) SetWriteDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWriteDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWriteDeadline indicates an expected call of SetWriteDeadline.
func (mr *MockNetConnMockRecorder) SetWriteDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteDeadline", reflect.TypeOf((*MockNetConn)(nil).SetWriteDeadline), t)
}

// Write mocks base method.
func (m *MockNetConn) Write(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockNetConnMockRecorder) Write(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockNetConn)(nil).Write), b)
}

// MockNetAddr is a mock of NetAddr interface.
type MockNetAddr struct {
	ctrl     *gomock.Controller
	recorder *MockNetAddrMockRecorder
	isgomock struct{}
}

// MockNetAddrMockRecorder is the mock recorder for MockNetAddr.
type MockNetAddrMockRecorder struct {
	mock *MockNetAddr
}

// NewMockNetAddr creates a new mock instance.
func NewMockNetAddr(ctrl *gomock.Controller) *MockNetAddr {
	mock := &MockNetAddr{ctrl: ctrl}
	mock.recorder = &MockNetAddrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetAddr) EXPECT() *MockNetAddrMockRecorder {
	return m.recorder
}

// Network mocks base method.
func (m *MockNetAddr) Network() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Network")
	ret0, _ := ret[0].(string)
	return ret0
}

// Network indicates an expected call of Network.
func (mr *MockNetAddrMockRecorder) Network() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Network", reflect.TypeOf((*MockNetAddr)(nil).Network))
}

// String mocks base method.
func (m *MockNetAddr) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockNetAddrMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockNetAddr)(nil).String))
}
