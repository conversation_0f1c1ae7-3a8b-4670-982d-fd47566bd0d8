// Code generated by MockGen. DO NOT EDIT.
// Source: io (interfaces: ReadWriteCloser)
//
// Generated by this command:
//
//	mockgen -package=mock_utils -destination=pkg/utils/mock_utils/io.go io ReadWriteCloser
//

// Package mock_utils is a generated GoMock package.
package mock_utils

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockReadWriteCloser is a mock of ReadWriteCloser interface.
type MockReadWriteCloser struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriteCloserMockRecorder
	isgomock struct{}
}

// MockReadWriteCloserMockRecorder is the mock recorder for MockReadWriteCloser.
type MockReadWriteCloserMockRecorder struct {
	mock *MockReadWriteCloser
}

// NewMockReadWriteCloser creates a new mock instance.
func NewMockReadWriteCloser(ctrl *gomock.Controller) *MockReadWriteCloser {
	mock := &MockReadWriteCloser{ctrl: ctrl}
	mock.recorder = &MockReadWriteCloserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriteCloser) EXPECT() *MockReadWriteCloserMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockReadWriteCloser) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockReadWriteCloserMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockReadWriteCloser)(nil).Close))
}

// Read mocks base method.
func (m *MockReadWriteCloser) Read(p []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", p)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read.
func (mr *MockReadWriteCloserMockRecorder) Read(p any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockReadWriteCloser)(nil).Read), p)
}

// Write mocks base method.
func (m *MockReadWriteCloser) Write(p []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", p)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockReadWriteCloserMockRecorder) Write(p any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockReadWriteCloser)(nil).Write), p)
}
