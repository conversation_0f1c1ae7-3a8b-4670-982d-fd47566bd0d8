// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/certificates/oser.go
//
// Generated by this command:
//
//	mockgen -source=pkg/certificates/oser.go -destination=pkg/certificates/mock_certificates/oser.go
//

// Package mock_certificates is a generated GoMock package.
package mock_certificates

import (
	fs "io/fs"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockOser is a mock of Oser interface.
type MockOser struct {
	ctrl     *gomock.Controller
	recorder *MockOserMockRecorder
	isgomock struct{}
}

// MockOserMockRecorder is the mock recorder for MockOser.
type MockOserMockRecorder struct {
	mock *MockOser
}

// NewMockOser creates a new mock instance.
func NewMockOser(ctrl *gomock.Controller) *MockOser {
	mock := &MockOser{ctrl: ctrl}
	mock.recorder = &MockOserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOser) EXPECT() *MockOserMockRecorder {
	return m.recorder
}

// ReadFile mocks base method.
func (m *MockOser) ReadFile(name string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFile", name)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadFile indicates an expected call of ReadFile.
func (mr *MockOserMockRecorder) ReadFile(name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFile", reflect.TypeOf((*MockOser)(nil).ReadFile), name)
}

// WriteFile mocks base method.
func (m *MockOser) WriteFile(name string, data []byte, perm fs.FileMode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteFile", name, data, perm)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteFile indicates an expected call of WriteFile.
func (mr *MockOserMockRecorder) WriteFile(name, data, perm any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteFile", reflect.TypeOf((*MockOser)(nil).WriteFile), name, data, perm)
}
