// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/certificates/rsaer.go
//
// Generated by this command:
//
//	mockgen -source=pkg/certificates/rsaer.go -destination=pkg/certificates/mock_certificates/rsaer.go
//

// Package mock_certificates is a generated GoMock package.
package mock_certificates

import (
	rsa "crypto/rsa"
	io "io"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRsaer is a mock of Rsaer interface.
type MockRsaer struct {
	ctrl     *gomock.Controller
	recorder *MockRsaerMockRecorder
	isgomock struct{}
}

// MockRsaerMockRecorder is the mock recorder for MockRsaer.
type MockRsaerMockRecorder struct {
	mock *MockRsaer
}

// NewMockRsaer creates a new mock instance.
func NewMockRsaer(ctrl *gomock.Controller) *MockRsaer {
	mock := &MockRsaer{ctrl: ctrl}
	mock.recorder = &MockRsaerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRsaer) EXPECT() *MockRsaerMockRecorder {
	return m.recorder
}

// GenerateKey mocks base method.
func (m *MockRsaer) GenerateKey(random io.Reader, bits int) (*rsa.PrivateKey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateKey", random, bits)
	ret0, _ := ret[0].(*rsa.PrivateKey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateKey indicates an expected call of GenerateKey.
func (mr *MockRsaerMockRecorder) GenerateKey(random, bits any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateKey", reflect.TypeOf((*MockRsaer)(nil).GenerateKey), random, bits)
}
