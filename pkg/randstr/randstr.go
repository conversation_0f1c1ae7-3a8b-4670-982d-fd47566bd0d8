package randstr

import (
	"crypto/rand"
	"math/big"
)

// RandomString returns a random string of a given length.
func RandomString(length int) string {
	if length < 0 {
		return ""
	}
	charset := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	randbytes := make([]byte, 0, length)
	for i := 0; i < length; i++ {
		idx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		randbytes = append(randbytes, charset[idx.Int64()])
	}

	return string(randbytes)
}
