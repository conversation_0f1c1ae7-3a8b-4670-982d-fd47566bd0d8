// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/framer/framer.go
//
// Generated by this command:
//
//	mockgen -source=pkg/framer/framer.go -destination=pkg/framer/mock_framer/framer.go
//

// Package mock_framer is a generated GoMock package.
package mock_framer

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockFramer is a mock of Framer interface.
type MockFramer struct {
	ctrl     *gomock.Controller
	recorder *MockFramerMockRecorder
	isgomock struct{}
}

// MockFramerMockRecorder is the mock recorder for MockFramer.
type MockFramerMockRecorder struct {
	mock *MockFramer
}

// NewMockFramer creates a new mock instance.
func NewMockFramer(ctrl *gomock.Controller) *MockFramer {
	mock := &MockFramer{ctrl: ctrl}
	mock.recorder = &MockFramerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFramer) EXPECT() *MockFramerMockRecorder {
	return m.recorder
}

// GetMessage mocks base method.
func (m *MockFramer) GetMessage() ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMessage")
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMessage indicates an expected call of GetMessage.
func (mr *MockFramerMockRecorder) GetMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMessage", reflect.TypeOf((*MockFramer)(nil).GetMessage))
}

// MessageReady mocks base method.
func (m *MockFramer) MessageReady() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MessageReady")
	ret0, _ := ret[0].(bool)
	return ret0
}

// MessageReady indicates an expected call of MessageReady.
func (mr *MockFramerMockRecorder) MessageReady() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MessageReady", reflect.TypeOf((*MockFramer)(nil).MessageReady))
}

// RecvData mocks base method.
func (m *MockFramer) RecvData(buf []byte) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RecvData", buf)
}

// RecvData indicates an expected call of RecvData.
func (mr *MockFramerMockRecorder) RecvData(buf any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvData", reflect.TypeOf((*MockFramer)(nil).RecvData), buf)
}

// SendData mocks base method.
func (m *MockFramer) SendData(data []byte) []byte {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendData", data)
	ret0, _ := ret[0].([]byte)
	return ret0
}

// SendData indicates an expected call of SendData.
func (mr *MockFramerMockRecorder) SendData(data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendData", reflect.TypeOf((*MockFramer)(nil).SendData), data)
}
