// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/workceptor/workceptor.go
//
// Generated by this command:
//
//	mockgen -source=pkg/workceptor/workceptor.go -destination=pkg/workceptor/mock_workceptor/workceptor.go
//

// Package mock_workceptor is a generated GoMock package.
package mock_workceptor

import (
	context "context"
	tls "crypto/tls"
	fs "io/fs"
	net "net"
	reflect "reflect"

	controlsvc "github.com/ansible/receptor/pkg/controlsvc"
	logger "github.com/ansible/receptor/pkg/logger"
	netceptor "github.com/ansible/receptor/pkg/netceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockNetceptorForWorkceptor is a mock of NetceptorForWorkceptor interface.
type MockNetceptorForWorkceptor struct {
	ctrl     *gomock.Controller
	recorder *MockNetceptorForWorkceptorMockRecorder
	isgomock struct{}
}

// MockNetceptorForWorkceptorMockRecorder is the mock recorder for MockNetceptorForWorkceptor.
type MockNetceptorForWorkceptorMockRecorder struct {
	mock *MockNetceptorForWorkceptor
}

// NewMockNetceptorForWorkceptor creates a new mock instance.
func NewMockNetceptorForWorkceptor(ctrl *gomock.Controller) *MockNetceptorForWorkceptor {
	mock := &MockNetceptorForWorkceptor{ctrl: ctrl}
	mock.recorder = &MockNetceptorForWorkceptorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetceptorForWorkceptor) EXPECT() *MockNetceptorForWorkceptorMockRecorder {
	return m.recorder
}

// AddWorkCommand mocks base method.
func (m *MockNetceptorForWorkceptor) AddWorkCommand(typeName string, verifySignature bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWorkCommand", typeName, verifySignature)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWorkCommand indicates an expected call of AddWorkCommand.
func (mr *MockNetceptorForWorkceptorMockRecorder) AddWorkCommand(typeName, verifySignature any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWorkCommand", reflect.TypeOf((*MockNetceptorForWorkceptor)(nil).AddWorkCommand), typeName, verifySignature)
}

// DialContext mocks base method.
func (m *MockNetceptorForWorkceptor) DialContext(ctx context.Context, node, service string, tlscfg *tls.Config) (*netceptor.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DialContext", ctx, node, service, tlscfg)
	ret0, _ := ret[0].(*netceptor.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DialContext indicates an expected call of DialContext.
func (mr *MockNetceptorForWorkceptorMockRecorder) DialContext(ctx, node, service, tlscfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DialContext", reflect.TypeOf((*MockNetceptorForWorkceptor)(nil).DialContext), ctx, node, service, tlscfg)
}

// GetClientTLSConfig mocks base method.
func (m *MockNetceptorForWorkceptor) GetClientTLSConfig(name, expectedHostName string, expectedHostNameType netceptor.ExpectedHostnameType) (*tls.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientTLSConfig", name, expectedHostName, expectedHostNameType)
	ret0, _ := ret[0].(*tls.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientTLSConfig indicates an expected call of GetClientTLSConfig.
func (mr *MockNetceptorForWorkceptorMockRecorder) GetClientTLSConfig(name, expectedHostName, expectedHostNameType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientTLSConfig", reflect.TypeOf((*MockNetceptorForWorkceptor)(nil).GetClientTLSConfig), name, expectedHostName, expectedHostNameType)
}

// GetLogger mocks base method.
func (m *MockNetceptorForWorkceptor) GetLogger() *logger.ReceptorLogger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogger")
	ret0, _ := ret[0].(*logger.ReceptorLogger)
	return ret0
}

// GetLogger indicates an expected call of GetLogger.
func (mr *MockNetceptorForWorkceptorMockRecorder) GetLogger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogger", reflect.TypeOf((*MockNetceptorForWorkceptor)(nil).GetLogger))
}

// NodeID mocks base method.
func (m *MockNetceptorForWorkceptor) NodeID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeID")
	ret0, _ := ret[0].(string)
	return ret0
}

// NodeID indicates an expected call of NodeID.
func (mr *MockNetceptorForWorkceptorMockRecorder) NodeID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeID", reflect.TypeOf((*MockNetceptorForWorkceptor)(nil).NodeID))
}

// MockServerForWorkceptor is a mock of ServerForWorkceptor interface.
type MockServerForWorkceptor struct {
	ctrl     *gomock.Controller
	recorder *MockServerForWorkceptorMockRecorder
	isgomock struct{}
}

// MockServerForWorkceptorMockRecorder is the mock recorder for MockServerForWorkceptor.
type MockServerForWorkceptorMockRecorder struct {
	mock *MockServerForWorkceptor
}

// NewMockServerForWorkceptor creates a new mock instance.
func NewMockServerForWorkceptor(ctrl *gomock.Controller) *MockServerForWorkceptor {
	mock := &MockServerForWorkceptor{ctrl: ctrl}
	mock.recorder = &MockServerForWorkceptorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServerForWorkceptor) EXPECT() *MockServerForWorkceptorMockRecorder {
	return m.recorder
}

// AddControlFunc mocks base method.
func (m *MockServerForWorkceptor) AddControlFunc(name string, cType controlsvc.ControlCommandType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddControlFunc", name, cType)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddControlFunc indicates an expected call of AddControlFunc.
func (mr *MockServerForWorkceptorMockRecorder) AddControlFunc(name, cType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddControlFunc", reflect.TypeOf((*MockServerForWorkceptor)(nil).AddControlFunc), name, cType)
}

// ConnectionListener mocks base method.
func (m *MockServerForWorkceptor) ConnectionListener(ctx context.Context, listener net.Listener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ConnectionListener", ctx, listener)
}

// ConnectionListener indicates an expected call of ConnectionListener.
func (mr *MockServerForWorkceptorMockRecorder) ConnectionListener(ctx, listener any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConnectionListener", reflect.TypeOf((*MockServerForWorkceptor)(nil).ConnectionListener), ctx, listener)
}

// RunControlSession mocks base method.
func (m *MockServerForWorkceptor) RunControlSession(conn net.Conn) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RunControlSession", conn)
}

// RunControlSession indicates an expected call of RunControlSession.
func (mr *MockServerForWorkceptorMockRecorder) RunControlSession(conn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunControlSession", reflect.TypeOf((*MockServerForWorkceptor)(nil).RunControlSession), conn)
}

// RunControlSvc mocks base method.
func (m *MockServerForWorkceptor) RunControlSvc(ctx context.Context, service string, tlscfg *tls.Config, unixSocket string, unixSocketPermissions fs.FileMode, tcpListen string, tcptls *tls.Config) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunControlSvc", ctx, service, tlscfg, unixSocket, unixSocketPermissions, tcpListen, tcptls)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunControlSvc indicates an expected call of RunControlSvc.
func (mr *MockServerForWorkceptorMockRecorder) RunControlSvc(ctx, service, tlscfg, unixSocket, unixSocketPermissions, tcpListen, tcptls any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunControlSvc", reflect.TypeOf((*MockServerForWorkceptor)(nil).RunControlSvc), ctx, service, tlscfg, unixSocket, unixSocketPermissions, tcpListen, tcptls)
}

// SetServerNet mocks base method.
func (m *MockServerForWorkceptor) SetServerNet(n controlsvc.Neter) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetServerNet", n)
}

// SetServerNet indicates an expected call of SetServerNet.
func (mr *MockServerForWorkceptorMockRecorder) SetServerNet(n any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetServerNet", reflect.TypeOf((*MockServerForWorkceptor)(nil).SetServerNet), n)
}

// SetServerTLS mocks base method.
func (m *MockServerForWorkceptor) SetServerTLS(t controlsvc.Tlser) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetServerTLS", t)
}

// SetServerTLS indicates an expected call of SetServerTLS.
func (mr *MockServerForWorkceptorMockRecorder) SetServerTLS(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetServerTLS", reflect.TypeOf((*MockServerForWorkceptor)(nil).SetServerTLS), t)
}

// SetServerUtils mocks base method.
func (m *MockServerForWorkceptor) SetServerUtils(u controlsvc.Utiler) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetServerUtils", u)
}

// SetServerUtils indicates an expected call of SetServerUtils.
func (mr *MockServerForWorkceptorMockRecorder) SetServerUtils(u any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetServerUtils", reflect.TypeOf((*MockServerForWorkceptor)(nil).SetServerUtils), u)
}

// SetupConnection mocks base method.
func (m *MockServerForWorkceptor) SetupConnection(conn net.Conn) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetupConnection", conn)
}

// SetupConnection indicates an expected call of SetupConnection.
func (mr *MockServerForWorkceptorMockRecorder) SetupConnection(conn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupConnection", reflect.TypeOf((*MockServerForWorkceptor)(nil).SetupConnection), conn)
}
