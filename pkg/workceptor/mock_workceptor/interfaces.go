// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/workceptor/interfaces.go
//
// Generated by this command:
//
//	mockgen -source=pkg/workceptor/interfaces.go -destination=pkg/workceptor/mock_workceptor/interfaces.go
//

// Package mock_workceptor is a generated GoMock package.
package mock_workceptor

import (
	reflect "reflect"

	workceptor "github.com/ansible/receptor/pkg/workceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockWorkUnit is a mock of WorkUnit interface.
type MockWorkUnit struct {
	ctrl     *gomock.Controller
	recorder *MockWorkUnitMockRecorder
	isgomock struct{}
}

// MockWorkUnitMockRecorder is the mock recorder for MockWorkUnit.
type MockWorkUnitMockRecorder struct {
	mock *MockWorkUnit
}

// NewMockWorkUnit creates a new mock instance.
func NewMockWorkUnit(ctrl *gomock.Controller) *MockWorkUnit {
	mock := &MockWorkUnit{ctrl: ctrl}
	mock.recorder = &MockWorkUnitMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkUnit) EXPECT() *MockWorkUnitMockRecorder {
	return m.recorder
}

// Cancel mocks base method.
func (m *MockWorkUnit) Cancel() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel")
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockWorkUnitMockRecorder) Cancel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockWorkUnit)(nil).Cancel))
}

// ID mocks base method.
func (m *MockWorkUnit) ID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ID indicates an expected call of ID.
func (mr *MockWorkUnitMockRecorder) ID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ID", reflect.TypeOf((*MockWorkUnit)(nil).ID))
}

// LastUpdateError mocks base method.
func (m *MockWorkUnit) LastUpdateError() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastUpdateError")
	ret0, _ := ret[0].(error)
	return ret0
}

// LastUpdateError indicates an expected call of LastUpdateError.
func (mr *MockWorkUnitMockRecorder) LastUpdateError() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastUpdateError", reflect.TypeOf((*MockWorkUnit)(nil).LastUpdateError))
}

// Load mocks base method.
func (m *MockWorkUnit) Load() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load")
	ret0, _ := ret[0].(error)
	return ret0
}

// Load indicates an expected call of Load.
func (mr *MockWorkUnitMockRecorder) Load() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockWorkUnit)(nil).Load))
}

// Release mocks base method.
func (m *MockWorkUnit) Release(force bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", force)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockWorkUnitMockRecorder) Release(force any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockWorkUnit)(nil).Release), force)
}

// Restart mocks base method.
func (m *MockWorkUnit) Restart() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Restart")
	ret0, _ := ret[0].(error)
	return ret0
}

// Restart indicates an expected call of Restart.
func (mr *MockWorkUnitMockRecorder) Restart() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Restart", reflect.TypeOf((*MockWorkUnit)(nil).Restart))
}

// Save mocks base method.
func (m *MockWorkUnit) Save() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save")
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockWorkUnitMockRecorder) Save() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockWorkUnit)(nil).Save))
}

// SetFromParams mocks base method.
func (m *MockWorkUnit) SetFromParams(params map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFromParams", params)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFromParams indicates an expected call of SetFromParams.
func (mr *MockWorkUnitMockRecorder) SetFromParams(params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFromParams", reflect.TypeOf((*MockWorkUnit)(nil).SetFromParams), params)
}

// Start mocks base method.
func (m *MockWorkUnit) Start() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start")
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockWorkUnitMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockWorkUnit)(nil).Start))
}

// Status mocks base method.
func (m *MockWorkUnit) Status() *workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(*workceptor.StatusFileData)
	return ret0
}

// Status indicates an expected call of Status.
func (mr *MockWorkUnitMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockWorkUnit)(nil).Status))
}

// StatusFileName mocks base method.
func (m *MockWorkUnit) StatusFileName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatusFileName")
	ret0, _ := ret[0].(string)
	return ret0
}

// StatusFileName indicates an expected call of StatusFileName.
func (mr *MockWorkUnitMockRecorder) StatusFileName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatusFileName", reflect.TypeOf((*MockWorkUnit)(nil).StatusFileName))
}

// StdoutFileName mocks base method.
func (m *MockWorkUnit) StdoutFileName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StdoutFileName")
	ret0, _ := ret[0].(string)
	return ret0
}

// StdoutFileName indicates an expected call of StdoutFileName.
func (mr *MockWorkUnitMockRecorder) StdoutFileName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StdoutFileName", reflect.TypeOf((*MockWorkUnit)(nil).StdoutFileName))
}

// UnitDir mocks base method.
func (m *MockWorkUnit) UnitDir() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnitDir")
	ret0, _ := ret[0].(string)
	return ret0
}

// UnitDir indicates an expected call of UnitDir.
func (mr *MockWorkUnitMockRecorder) UnitDir() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnitDir", reflect.TypeOf((*MockWorkUnit)(nil).UnitDir))
}

// UnredactedStatus mocks base method.
func (m *MockWorkUnit) UnredactedStatus() *workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnredactedStatus")
	ret0, _ := ret[0].(*workceptor.StatusFileData)
	return ret0
}

// UnredactedStatus indicates an expected call of UnredactedStatus.
func (mr *MockWorkUnitMockRecorder) UnredactedStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnredactedStatus", reflect.TypeOf((*MockWorkUnit)(nil).UnredactedStatus))
}

// UpdateBasicStatus mocks base method.
func (m *MockWorkUnit) UpdateBasicStatus(state int, detail string, stdoutSize int64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateBasicStatus", state, detail, stdoutSize)
}

// UpdateBasicStatus indicates an expected call of UpdateBasicStatus.
func (mr *MockWorkUnitMockRecorder) UpdateBasicStatus(state, detail, stdoutSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBasicStatus", reflect.TypeOf((*MockWorkUnit)(nil).UpdateBasicStatus), state, detail, stdoutSize)
}

// UpdateFullStatus mocks base method.
func (m *MockWorkUnit) UpdateFullStatus(statusFunc func(*workceptor.StatusFileData)) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateFullStatus", statusFunc)
}

// UpdateFullStatus indicates an expected call of UpdateFullStatus.
func (mr *MockWorkUnitMockRecorder) UpdateFullStatus(statusFunc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullStatus", reflect.TypeOf((*MockWorkUnit)(nil).UpdateFullStatus), statusFunc)
}

// MockWorkerConfig is a mock of WorkerConfig interface.
type MockWorkerConfig struct {
	ctrl     *gomock.Controller
	recorder *MockWorkerConfigMockRecorder
	isgomock struct{}
}

// MockWorkerConfigMockRecorder is the mock recorder for MockWorkerConfig.
type MockWorkerConfigMockRecorder struct {
	mock *MockWorkerConfig
}

// NewMockWorkerConfig creates a new mock instance.
func NewMockWorkerConfig(ctrl *gomock.Controller) *MockWorkerConfig {
	mock := &MockWorkerConfig{ctrl: ctrl}
	mock.recorder = &MockWorkerConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkerConfig) EXPECT() *MockWorkerConfigMockRecorder {
	return m.recorder
}

// GetVerifySignature mocks base method.
func (m *MockWorkerConfig) GetVerifySignature() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVerifySignature")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetVerifySignature indicates an expected call of GetVerifySignature.
func (mr *MockWorkerConfigMockRecorder) GetVerifySignature() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVerifySignature", reflect.TypeOf((*MockWorkerConfig)(nil).GetVerifySignature))
}

// GetWorkType mocks base method.
func (m *MockWorkerConfig) GetWorkType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkType")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetWorkType indicates an expected call of GetWorkType.
func (mr *MockWorkerConfigMockRecorder) GetWorkType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkType", reflect.TypeOf((*MockWorkerConfig)(nil).GetWorkType))
}

// NewWorker mocks base method.
func (m *MockWorkerConfig) NewWorker(bwu workceptor.BaseWorkUnitForWorkUnit, w *workceptor.Workceptor, unitID, workType string) workceptor.WorkUnit {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewWorker", bwu, w, unitID, workType)
	ret0, _ := ret[0].(workceptor.WorkUnit)
	return ret0
}

// NewWorker indicates an expected call of NewWorker.
func (mr *MockWorkerConfigMockRecorder) NewWorker(bwu, w, unitID, workType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewWorker", reflect.TypeOf((*MockWorkerConfig)(nil).NewWorker), bwu, w, unitID, workType)
}
