// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/workceptor/command.go
//
// Generated by this command:
//
//	mockgen -source=pkg/workceptor/command.go -destination=pkg/workceptor/mock_workceptor/command.go
//

// Package mock_workceptor is a generated GoMock package.
package mock_workceptor

import (
	context "context"
	reflect "reflect"
	sync "sync"

	workceptor "github.com/ansible/receptor/pkg/workceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockBaseWorkUnitForWorkUnit is a mock of BaseWorkUnitForWorkUnit interface.
type MockBaseWorkUnitForWorkUnit struct {
	ctrl     *gomock.Controller
	recorder *MockBaseWorkUnitForWorkUnitMockRecorder
	isgomock struct{}
}

// MockBaseWorkUnitForWorkUnitMockRecorder is the mock recorder for MockBaseWorkUnitForWorkUnit.
type MockBaseWorkUnitForWorkUnitMockRecorder struct {
	mock *MockBaseWorkUnitForWorkUnit
}

// NewMockBaseWorkUnitForWorkUnit creates a new mock instance.
func NewMockBaseWorkUnitForWorkUnit(ctrl *gomock.Controller) *MockBaseWorkUnitForWorkUnit {
	mock := &MockBaseWorkUnitForWorkUnit{ctrl: ctrl}
	mock.recorder = &MockBaseWorkUnitForWorkUnitMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBaseWorkUnitForWorkUnit) EXPECT() *MockBaseWorkUnitForWorkUnitMockRecorder {
	return m.recorder
}

// CancelContext mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) CancelContext() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CancelContext")
}

// CancelContext indicates an expected call of CancelContext.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) CancelContext() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContext", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).CancelContext))
}

// GetCancel mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetCancel() context.CancelFunc {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCancel")
	ret0, _ := ret[0].(context.CancelFunc)
	return ret0
}

// GetCancel indicates an expected call of GetCancel.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetCancel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCancel", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetCancel))
}

// GetContext mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetContext() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContext")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// GetContext indicates an expected call of GetContext.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetContext() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContext", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetContext))
}

// GetStatusCopy mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetStatusCopy() workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatusCopy")
	ret0, _ := ret[0].(workceptor.StatusFileData)
	return ret0
}

// GetStatusCopy indicates an expected call of GetStatusCopy.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetStatusCopy() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusCopy", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetStatusCopy))
}

// GetStatusLock mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetStatusLock() *sync.RWMutex {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatusLock")
	ret0, _ := ret[0].(*sync.RWMutex)
	return ret0
}

// GetStatusLock indicates an expected call of GetStatusLock.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetStatusLock() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusLock", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetStatusLock))
}

// GetStatusWithoutExtraData mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetStatusWithoutExtraData() *workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatusWithoutExtraData")
	ret0, _ := ret[0].(*workceptor.StatusFileData)
	return ret0
}

// GetStatusWithoutExtraData indicates an expected call of GetStatusWithoutExtraData.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetStatusWithoutExtraData() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusWithoutExtraData", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetStatusWithoutExtraData))
}

// GetWorkceptor mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) GetWorkceptor() *workceptor.Workceptor {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkceptor")
	ret0, _ := ret[0].(*workceptor.Workceptor)
	return ret0
}

// GetWorkceptor indicates an expected call of GetWorkceptor.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) GetWorkceptor() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkceptor", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).GetWorkceptor))
}

// ID mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) ID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ID indicates an expected call of ID.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) ID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ID", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).ID))
}

// Init mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) Init(w *workceptor.Workceptor, unitID, workType string, fs workceptor.FileSystemer) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Init", w, unitID, workType, fs)
}

// Init indicates an expected call of Init.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) Init(w, unitID, workType, fs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).Init), w, unitID, workType, fs)
}

// LastUpdateError mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) LastUpdateError() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastUpdateError")
	ret0, _ := ret[0].(error)
	return ret0
}

// LastUpdateError indicates an expected call of LastUpdateError.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) LastUpdateError() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastUpdateError", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).LastUpdateError))
}

// Load mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) Load() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load")
	ret0, _ := ret[0].(error)
	return ret0
}

// Load indicates an expected call of Load.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) Load() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).Load))
}

// MonitorLocalStatus mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) MonitorLocalStatus() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MonitorLocalStatus")
}

// MonitorLocalStatus indicates an expected call of MonitorLocalStatus.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) MonitorLocalStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MonitorLocalStatus", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).MonitorLocalStatus))
}

// Release mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) Release(force bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", force)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) Release(force any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).Release), force)
}

// Save mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) Save() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save")
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) Save() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).Save))
}

// SetFromParams mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) SetFromParams(arg0 map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFromParams", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFromParams indicates an expected call of SetFromParams.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) SetFromParams(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFromParams", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).SetFromParams), arg0)
}

// SetStatusExtraData mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) SetStatusExtraData(arg0 any) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetStatusExtraData", arg0)
}

// SetStatusExtraData indicates an expected call of SetStatusExtraData.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) SetStatusExtraData(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetStatusExtraData", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).SetStatusExtraData), arg0)
}

// SetWorkceptor mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) SetWorkceptor(arg0 *workceptor.Workceptor) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetWorkceptor", arg0)
}

// SetWorkceptor indicates an expected call of SetWorkceptor.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) SetWorkceptor(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWorkceptor", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).SetWorkceptor), arg0)
}

// Status mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) Status() *workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(*workceptor.StatusFileData)
	return ret0
}

// Status indicates an expected call of Status.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).Status))
}

// StatusFileName mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) StatusFileName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatusFileName")
	ret0, _ := ret[0].(string)
	return ret0
}

// StatusFileName indicates an expected call of StatusFileName.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) StatusFileName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatusFileName", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).StatusFileName))
}

// StdoutFileName mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) StdoutFileName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StdoutFileName")
	ret0, _ := ret[0].(string)
	return ret0
}

// StdoutFileName indicates an expected call of StdoutFileName.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) StdoutFileName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StdoutFileName", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).StdoutFileName))
}

// UnitDir mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) UnitDir() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnitDir")
	ret0, _ := ret[0].(string)
	return ret0
}

// UnitDir indicates an expected call of UnitDir.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) UnitDir() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnitDir", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).UnitDir))
}

// UnredactedStatus mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) UnredactedStatus() *workceptor.StatusFileData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnredactedStatus")
	ret0, _ := ret[0].(*workceptor.StatusFileData)
	return ret0
}

// UnredactedStatus indicates an expected call of UnredactedStatus.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) UnredactedStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnredactedStatus", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).UnredactedStatus))
}

// UpdateBasicStatus mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) UpdateBasicStatus(state int, detail string, stdoutSize int64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateBasicStatus", state, detail, stdoutSize)
}

// UpdateBasicStatus indicates an expected call of UpdateBasicStatus.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) UpdateBasicStatus(state, detail, stdoutSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBasicStatus", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).UpdateBasicStatus), state, detail, stdoutSize)
}

// UpdateFullStatus mocks base method.
func (m *MockBaseWorkUnitForWorkUnit) UpdateFullStatus(statusFunc func(*workceptor.StatusFileData)) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateFullStatus", statusFunc)
}

// UpdateFullStatus indicates an expected call of UpdateFullStatus.
func (mr *MockBaseWorkUnitForWorkUnitMockRecorder) UpdateFullStatus(statusFunc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullStatus", reflect.TypeOf((*MockBaseWorkUnitForWorkUnit)(nil).UpdateFullStatus), statusFunc)
}
