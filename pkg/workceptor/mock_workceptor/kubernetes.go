// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/workceptor/kubernetes.go
//
// Generated by this command:
//
//	mockgen -source=pkg/workceptor/kubernetes.go -destination=pkg/workceptor/mock_workceptor/kubernetes.go
//

// Package mock_workceptor is a generated GoMock package.
package mock_workceptor

import (
	context "context"
	url "net/url"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	v1 "k8s.io/api/core/v1"
	errors "k8s.io/apimachinery/pkg/api/errors"
	v10 "k8s.io/apimachinery/pkg/apis/meta/v1"
	fields "k8s.io/apimachinery/pkg/fields"
	runtime "k8s.io/apimachinery/pkg/runtime"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	watch "k8s.io/apimachinery/pkg/watch"
	kubernetes "k8s.io/client-go/kubernetes"
	rest "k8s.io/client-go/rest"
	cache "k8s.io/client-go/tools/cache"
	clientcmd "k8s.io/client-go/tools/clientcmd"
	remotecommand "k8s.io/client-go/tools/remotecommand"
	watch0 "k8s.io/client-go/tools/watch"
	flowcontrol "k8s.io/client-go/util/flowcontrol"
)

// MockKubeAPIer is a mock of KubeAPIer interface.
type MockKubeAPIer struct {
	ctrl     *gomock.Controller
	recorder *MockKubeAPIerMockRecorder
	isgomock struct{}
}

// MockKubeAPIerMockRecorder is the mock recorder for MockKubeAPIer.
type MockKubeAPIerMockRecorder struct {
	mock *MockKubeAPIer
}

// NewMockKubeAPIer creates a new mock instance.
func NewMockKubeAPIer(ctrl *gomock.Controller) *MockKubeAPIer {
	mock := &MockKubeAPIer{ctrl: ctrl}
	mock.recorder = &MockKubeAPIerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKubeAPIer) EXPECT() *MockKubeAPIerMockRecorder {
	return m.recorder
}

// BuildConfigFromFlags mocks base method.
func (m *MockKubeAPIer) BuildConfigFromFlags(arg0, arg1 string) (*rest.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildConfigFromFlags", arg0, arg1)
	ret0, _ := ret[0].(*rest.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildConfigFromFlags indicates an expected call of BuildConfigFromFlags.
func (mr *MockKubeAPIerMockRecorder) BuildConfigFromFlags(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildConfigFromFlags", reflect.TypeOf((*MockKubeAPIer)(nil).BuildConfigFromFlags), arg0, arg1)
}

// Create mocks base method.
func (m *MockKubeAPIer) Create(arg0 context.Context, arg1 *kubernetes.Clientset, arg2 string, arg3 *v1.Pod, arg4 v10.CreateOptions) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockKubeAPIerMockRecorder) Create(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKubeAPIer)(nil).Create), arg0, arg1, arg2, arg3, arg4)
}

// Delete mocks base method.
func (m *MockKubeAPIer) Delete(arg0 context.Context, arg1 *kubernetes.Clientset, arg2, arg3 string, arg4 v10.DeleteOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockKubeAPIerMockRecorder) Delete(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKubeAPIer)(nil).Delete), arg0, arg1, arg2, arg3, arg4)
}

// Get mocks base method.
func (m *MockKubeAPIer) Get(arg0 context.Context, arg1 *kubernetes.Clientset, arg2, arg3 string, arg4 v10.GetOptions) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockKubeAPIerMockRecorder) Get(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockKubeAPIer)(nil).Get), arg0, arg1, arg2, arg3, arg4)
}

// GetLogs mocks base method.
func (m *MockKubeAPIer) GetLogs(arg0 *kubernetes.Clientset, arg1, arg2 string, arg3 *v1.PodLogOptions) *rest.Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*rest.Request)
	return ret0
}

// GetLogs indicates an expected call of GetLogs.
func (mr *MockKubeAPIerMockRecorder) GetLogs(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogs", reflect.TypeOf((*MockKubeAPIer)(nil).GetLogs), arg0, arg1, arg2, arg3)
}

// InClusterConfig mocks base method.
func (m *MockKubeAPIer) InClusterConfig() (*rest.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InClusterConfig")
	ret0, _ := ret[0].(*rest.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InClusterConfig indicates an expected call of InClusterConfig.
func (mr *MockKubeAPIerMockRecorder) InClusterConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InClusterConfig", reflect.TypeOf((*MockKubeAPIer)(nil).InClusterConfig))
}

// List mocks base method.
func (m *MockKubeAPIer) List(arg0 context.Context, arg1 *kubernetes.Clientset, arg2 string, arg3 v10.ListOptions) (*v1.PodList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*v1.PodList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockKubeAPIerMockRecorder) List(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockKubeAPIer)(nil).List), arg0, arg1, arg2, arg3)
}

// NewClientConfigFromBytes mocks base method.
func (m *MockKubeAPIer) NewClientConfigFromBytes(arg0 []byte) (clientcmd.ClientConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewClientConfigFromBytes", arg0)
	ret0, _ := ret[0].(clientcmd.ClientConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewClientConfigFromBytes indicates an expected call of NewClientConfigFromBytes.
func (mr *MockKubeAPIerMockRecorder) NewClientConfigFromBytes(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewClientConfigFromBytes", reflect.TypeOf((*MockKubeAPIer)(nil).NewClientConfigFromBytes), arg0)
}

// NewDefaultClientConfigLoadingRules mocks base method.
func (m *MockKubeAPIer) NewDefaultClientConfigLoadingRules() *clientcmd.ClientConfigLoadingRules {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewDefaultClientConfigLoadingRules")
	ret0, _ := ret[0].(*clientcmd.ClientConfigLoadingRules)
	return ret0
}

// NewDefaultClientConfigLoadingRules indicates an expected call of NewDefaultClientConfigLoadingRules.
func (mr *MockKubeAPIerMockRecorder) NewDefaultClientConfigLoadingRules() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewDefaultClientConfigLoadingRules", reflect.TypeOf((*MockKubeAPIer)(nil).NewDefaultClientConfigLoadingRules))
}

// NewFakeAlwaysRateLimiter mocks base method.
func (m *MockKubeAPIer) NewFakeAlwaysRateLimiter() flowcontrol.RateLimiter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewFakeAlwaysRateLimiter")
	ret0, _ := ret[0].(flowcontrol.RateLimiter)
	return ret0
}

// NewFakeAlwaysRateLimiter indicates an expected call of NewFakeAlwaysRateLimiter.
func (mr *MockKubeAPIerMockRecorder) NewFakeAlwaysRateLimiter() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewFakeAlwaysRateLimiter", reflect.TypeOf((*MockKubeAPIer)(nil).NewFakeAlwaysRateLimiter))
}

// NewFakeNeverRateLimiter mocks base method.
func (m *MockKubeAPIer) NewFakeNeverRateLimiter() flowcontrol.RateLimiter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewFakeNeverRateLimiter")
	ret0, _ := ret[0].(flowcontrol.RateLimiter)
	return ret0
}

// NewFakeNeverRateLimiter indicates an expected call of NewFakeNeverRateLimiter.
func (mr *MockKubeAPIerMockRecorder) NewFakeNeverRateLimiter() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewFakeNeverRateLimiter", reflect.TypeOf((*MockKubeAPIer)(nil).NewFakeNeverRateLimiter))
}

// NewForConfig mocks base method.
func (m *MockKubeAPIer) NewForConfig(arg0 *rest.Config) (*kubernetes.Clientset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewForConfig", arg0)
	ret0, _ := ret[0].(*kubernetes.Clientset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewForConfig indicates an expected call of NewForConfig.
func (mr *MockKubeAPIerMockRecorder) NewForConfig(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewForConfig", reflect.TypeOf((*MockKubeAPIer)(nil).NewForConfig), arg0)
}

// NewNotFound mocks base method.
func (m *MockKubeAPIer) NewNotFound(arg0 schema.GroupResource, arg1 string) *errors.StatusError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewNotFound", arg0, arg1)
	ret0, _ := ret[0].(*errors.StatusError)
	return ret0
}

// NewNotFound indicates an expected call of NewNotFound.
func (mr *MockKubeAPIerMockRecorder) NewNotFound(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewNotFound", reflect.TypeOf((*MockKubeAPIer)(nil).NewNotFound), arg0, arg1)
}

// NewSPDYExecutor mocks base method.
func (m *MockKubeAPIer) NewSPDYExecutor(arg0 *rest.Config, arg1 string, arg2 *url.URL) (remotecommand.Executor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSPDYExecutor", arg0, arg1, arg2)
	ret0, _ := ret[0].(remotecommand.Executor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSPDYExecutor indicates an expected call of NewSPDYExecutor.
func (mr *MockKubeAPIerMockRecorder) NewSPDYExecutor(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSPDYExecutor", reflect.TypeOf((*MockKubeAPIer)(nil).NewSPDYExecutor), arg0, arg1, arg2)
}

// OneTermEqualSelector mocks base method.
func (m *MockKubeAPIer) OneTermEqualSelector(arg0, arg1 string) fields.Selector {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OneTermEqualSelector", arg0, arg1)
	ret0, _ := ret[0].(fields.Selector)
	return ret0
}

// OneTermEqualSelector indicates an expected call of OneTermEqualSelector.
func (mr *MockKubeAPIerMockRecorder) OneTermEqualSelector(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OneTermEqualSelector", reflect.TypeOf((*MockKubeAPIer)(nil).OneTermEqualSelector), arg0, arg1)
}

// StreamWithContext mocks base method.
func (m *MockKubeAPIer) StreamWithContext(arg0 context.Context, arg1 remotecommand.Executor, arg2 remotecommand.StreamOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamWithContext", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// StreamWithContext indicates an expected call of StreamWithContext.
func (mr *MockKubeAPIerMockRecorder) StreamWithContext(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamWithContext", reflect.TypeOf((*MockKubeAPIer)(nil).StreamWithContext), arg0, arg1, arg2)
}

// SubResource mocks base method.
func (m *MockKubeAPIer) SubResource(arg0 *kubernetes.Clientset, arg1, arg2 string) *rest.Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubResource", arg0, arg1, arg2)
	ret0, _ := ret[0].(*rest.Request)
	return ret0
}

// SubResource indicates an expected call of SubResource.
func (mr *MockKubeAPIerMockRecorder) SubResource(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubResource", reflect.TypeOf((*MockKubeAPIer)(nil).SubResource), arg0, arg1, arg2)
}

// UntilWithSync mocks base method.
func (m *MockKubeAPIer) UntilWithSync(arg0 context.Context, arg1 cache.ListerWatcher, arg2 runtime.Object, arg3 watch0.PreconditionFunc, arg4 ...watch0.ConditionFunc) (*watch.Event, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UntilWithSync", varargs...)
	ret0, _ := ret[0].(*watch.Event)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UntilWithSync indicates an expected call of UntilWithSync.
func (mr *MockKubeAPIerMockRecorder) UntilWithSync(arg0, arg1, arg2, arg3 any, arg4 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntilWithSync", reflect.TypeOf((*MockKubeAPIer)(nil).UntilWithSync), varargs...)
}

// Watch mocks base method.
func (m *MockKubeAPIer) Watch(arg0 context.Context, arg1 *kubernetes.Clientset, arg2 string, arg3 v10.ListOptions) (watch.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Watch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(watch.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Watch indicates an expected call of Watch.
func (mr *MockKubeAPIerMockRecorder) Watch(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockKubeAPIer)(nil).Watch), arg0, arg1, arg2, arg3)
}
