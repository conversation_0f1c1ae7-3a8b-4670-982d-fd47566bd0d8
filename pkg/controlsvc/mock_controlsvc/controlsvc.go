// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/controlsvc/controlsvc.go
//
// Generated by this command:
//
//	mockgen -source=pkg/controlsvc/controlsvc.go -destination=pkg/controlsvc/mock_controlsvc/controlsvc.go
//

// Package mock_controlsvc is a generated GoMock package.
package mock_controlsvc

import (
	context "context"
	tls "crypto/tls"
	io "io"
	fs "io/fs"
	net "net"
	reflect "reflect"
	time "time"

	logger "github.com/ansible/receptor/pkg/logger"
	netceptor "github.com/ansible/receptor/pkg/netceptor"
	utils "github.com/ansible/receptor/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

// MockCopier is a mock of Copier interface.
type MockCopier struct {
	ctrl     *gomock.Controller
	recorder *MockCopierMockRecorder
	isgomock struct{}
}

// MockCopierMockRecorder is the mock recorder for MockCopier.
type MockCopierMockRecorder struct {
	mock *MockCopier
}

// NewMockCopier creates a new mock instance.
func NewMockCopier(ctrl *gomock.Controller) *MockCopier {
	mock := &MockCopier{ctrl: ctrl}
	mock.recorder = &MockCopierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCopier) EXPECT() *MockCopierMockRecorder {
	return m.recorder
}

// Copy mocks base method.
func (m *MockCopier) Copy(dst io.Writer, src io.Reader) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Copy", dst, src)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Copy indicates an expected call of Copy.
func (mr *MockCopierMockRecorder) Copy(dst, src any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Copy", reflect.TypeOf((*MockCopier)(nil).Copy), dst, src)
}

// MockNetceptorForControlsvc is a mock of NetceptorForControlsvc interface.
type MockNetceptorForControlsvc struct {
	ctrl     *gomock.Controller
	recorder *MockNetceptorForControlsvcMockRecorder
	isgomock struct{}
}

// MockNetceptorForControlsvcMockRecorder is the mock recorder for MockNetceptorForControlsvc.
type MockNetceptorForControlsvcMockRecorder struct {
	mock *MockNetceptorForControlsvc
}

// NewMockNetceptorForControlsvc creates a new mock instance.
func NewMockNetceptorForControlsvc(ctrl *gomock.Controller) *MockNetceptorForControlsvc {
	mock := &MockNetceptorForControlsvc{ctrl: ctrl}
	mock.recorder = &MockNetceptorForControlsvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetceptorForControlsvc) EXPECT() *MockNetceptorForControlsvcMockRecorder {
	return m.recorder
}

// CancelBackends mocks base method.
func (m *MockNetceptorForControlsvc) CancelBackends() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CancelBackends")
}

// CancelBackends indicates an expected call of CancelBackends.
func (mr *MockNetceptorForControlsvcMockRecorder) CancelBackends() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelBackends", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).CancelBackends))
}

// Dial mocks base method.
func (m *MockNetceptorForControlsvc) Dial(node, service string, tlscfg *tls.Config) (*netceptor.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dial", node, service, tlscfg)
	ret0, _ := ret[0].(*netceptor.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dial indicates an expected call of Dial.
func (mr *MockNetceptorForControlsvcMockRecorder) Dial(node, service, tlscfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dial", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).Dial), node, service, tlscfg)
}

// GetClientTLSConfig mocks base method.
func (m *MockNetceptorForControlsvc) GetClientTLSConfig(name, expectedHostName string, expectedHostNameType netceptor.ExpectedHostnameType) (*tls.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientTLSConfig", name, expectedHostName, expectedHostNameType)
	ret0, _ := ret[0].(*tls.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientTLSConfig indicates an expected call of GetClientTLSConfig.
func (mr *MockNetceptorForControlsvcMockRecorder) GetClientTLSConfig(name, expectedHostName, expectedHostNameType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientTLSConfig", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).GetClientTLSConfig), name, expectedHostName, expectedHostNameType)
}

// GetLogger mocks base method.
func (m *MockNetceptorForControlsvc) GetLogger() *logger.ReceptorLogger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogger")
	ret0, _ := ret[0].(*logger.ReceptorLogger)
	return ret0
}

// GetLogger indicates an expected call of GetLogger.
func (mr *MockNetceptorForControlsvcMockRecorder) GetLogger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogger", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).GetLogger))
}

// ListenAndAdvertise mocks base method.
func (m *MockNetceptorForControlsvc) ListenAndAdvertise(service string, tlscfg *tls.Config, tags map[string]string) (*netceptor.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListenAndAdvertise", service, tlscfg, tags)
	ret0, _ := ret[0].(*netceptor.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListenAndAdvertise indicates an expected call of ListenAndAdvertise.
func (mr *MockNetceptorForControlsvcMockRecorder) ListenAndAdvertise(service, tlscfg, tags any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListenAndAdvertise", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).ListenAndAdvertise), service, tlscfg, tags)
}

// MaxForwardingHops mocks base method.
func (m *MockNetceptorForControlsvc) MaxForwardingHops() byte {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MaxForwardingHops")
	ret0, _ := ret[0].(byte)
	return ret0
}

// MaxForwardingHops indicates an expected call of MaxForwardingHops.
func (mr *MockNetceptorForControlsvcMockRecorder) MaxForwardingHops() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MaxForwardingHops", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).MaxForwardingHops))
}

// NodeID mocks base method.
func (m *MockNetceptorForControlsvc) NodeID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeID")
	ret0, _ := ret[0].(string)
	return ret0
}

// NodeID indicates an expected call of NodeID.
func (mr *MockNetceptorForControlsvcMockRecorder) NodeID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeID", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).NodeID))
}

// Ping mocks base method.
func (m *MockNetceptorForControlsvc) Ping(ctx context.Context, target string, hopsToLive byte) (time.Duration, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", ctx, target, hopsToLive)
	ret0, _ := ret[0].(time.Duration)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Ping indicates an expected call of Ping.
func (mr *MockNetceptorForControlsvcMockRecorder) Ping(ctx, target, hopsToLive any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).Ping), ctx, target, hopsToLive)
}

// Status mocks base method.
func (m *MockNetceptorForControlsvc) Status() netceptor.Status {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(netceptor.Status)
	return ret0
}

// Status indicates an expected call of Status.
func (mr *MockNetceptorForControlsvcMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).Status))
}

// Traceroute mocks base method.
func (m *MockNetceptorForControlsvc) Traceroute(ctx context.Context, target string) <-chan *netceptor.TracerouteResult {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Traceroute", ctx, target)
	ret0, _ := ret[0].(<-chan *netceptor.TracerouteResult)
	return ret0
}

// Traceroute indicates an expected call of Traceroute.
func (mr *MockNetceptorForControlsvcMockRecorder) Traceroute(ctx, target any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Traceroute", reflect.TypeOf((*MockNetceptorForControlsvc)(nil).Traceroute), ctx, target)
}

// MockUtiler is a mock of Utiler interface.
type MockUtiler struct {
	ctrl     *gomock.Controller
	recorder *MockUtilerMockRecorder
	isgomock struct{}
}

// MockUtilerMockRecorder is the mock recorder for MockUtiler.
type MockUtilerMockRecorder struct {
	mock *MockUtiler
}

// NewMockUtiler creates a new mock instance.
func NewMockUtiler(ctrl *gomock.Controller) *MockUtiler {
	mock := &MockUtiler{ctrl: ctrl}
	mock.recorder = &MockUtilerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUtiler) EXPECT() *MockUtilerMockRecorder {
	return m.recorder
}

// BridgeConns mocks base method.
func (m *MockUtiler) BridgeConns(c1 io.ReadWriteCloser, c1Name string, c2 io.ReadWriteCloser, c2Name string, logger *logger.ReceptorLogger) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "BridgeConns", c1, c1Name, c2, c2Name, logger)
}

// BridgeConns indicates an expected call of BridgeConns.
func (mr *MockUtilerMockRecorder) BridgeConns(c1, c1Name, c2, c2Name, logger any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BridgeConns", reflect.TypeOf((*MockUtiler)(nil).BridgeConns), c1, c1Name, c2, c2Name, logger)
}

// UnixSocketListen mocks base method.
func (m *MockUtiler) UnixSocketListen(filename string, permissions fs.FileMode) (net.Listener, *utils.FLock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnixSocketListen", filename, permissions)
	ret0, _ := ret[0].(net.Listener)
	ret1, _ := ret[1].(*utils.FLock)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UnixSocketListen indicates an expected call of UnixSocketListen.
func (mr *MockUtilerMockRecorder) UnixSocketListen(filename, permissions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnixSocketListen", reflect.TypeOf((*MockUtiler)(nil).UnixSocketListen), filename, permissions)
}

// MockNeter is a mock of Neter interface.
type MockNeter struct {
	ctrl     *gomock.Controller
	recorder *MockNeterMockRecorder
	isgomock struct{}
}

// MockNeterMockRecorder is the mock recorder for MockNeter.
type MockNeterMockRecorder struct {
	mock *MockNeter
}

// NewMockNeter creates a new mock instance.
func NewMockNeter(ctrl *gomock.Controller) *MockNeter {
	mock := &MockNeter{ctrl: ctrl}
	mock.recorder = &MockNeterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNeter) EXPECT() *MockNeterMockRecorder {
	return m.recorder
}

// Listen mocks base method.
func (m *MockNeter) Listen(network, address string) (net.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Listen", network, address)
	ret0, _ := ret[0].(net.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Listen indicates an expected call of Listen.
func (mr *MockNeterMockRecorder) Listen(network, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Listen", reflect.TypeOf((*MockNeter)(nil).Listen), network, address)
}

// MockTlser is a mock of Tlser interface.
type MockTlser struct {
	ctrl     *gomock.Controller
	recorder *MockTlserMockRecorder
	isgomock struct{}
}

// MockTlserMockRecorder is the mock recorder for MockTlser.
type MockTlserMockRecorder struct {
	mock *MockTlser
}

// NewMockTlser creates a new mock instance.
func NewMockTlser(ctrl *gomock.Controller) *MockTlser {
	mock := &MockTlser{ctrl: ctrl}
	mock.recorder = &MockTlserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTlser) EXPECT() *MockTlserMockRecorder {
	return m.recorder
}

// NewListener mocks base method.
func (m *MockTlser) NewListener(inner net.Listener, config *tls.Config) net.Listener {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewListener", inner, config)
	ret0, _ := ret[0].(net.Listener)
	return ret0
}

// NewListener indicates an expected call of NewListener.
func (mr *MockTlserMockRecorder) NewListener(inner, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewListener", reflect.TypeOf((*MockTlser)(nil).NewListener), inner, config)
}
