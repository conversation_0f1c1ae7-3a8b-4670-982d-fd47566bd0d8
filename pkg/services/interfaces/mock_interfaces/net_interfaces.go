// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/interfaces/net_interfaces.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/interfaces/net_interfaces.go -destination=pkg/services/interfaces/mock_interfaces/net_interfaces.go
//

// Package mock_netinterface is a generated GoMock package.
package mock_netinterface

import (
	net "net"
	netip "net/netip"
	os "os"
	reflect "reflect"
	syscall "syscall"
	time "time"

	netinterface "github.com/ansible/receptor/pkg/services/interfaces"
	gomock "go.uber.org/mock/gomock"
)

// MockNetterUDP is a mock of NetterUDP interface.
type MockNetterUDP struct {
	ctrl     *gomock.Controller
	recorder *MockNetterUDPMockRecorder
	isgomock struct{}
}

// MockNetterUDPMockRecorder is the mock recorder for MockNetterUDP.
type MockNetterUDPMockRecorder struct {
	mock *MockNetterUDP
}

// NewMockNetterUDP creates a new mock instance.
func NewMockNetterUDP(ctrl *gomock.Controller) *MockNetterUDP {
	mock := &MockNetterUDP{ctrl: ctrl}
	mock.recorder = &MockNetterUDPMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetterUDP) EXPECT() *MockNetterUDPMockRecorder {
	return m.recorder
}

// DialUDP mocks base method.
func (m *MockNetterUDP) DialUDP(network string, laddr, raddr *net.UDPAddr) (netinterface.UDPConnInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DialUDP", network, laddr, raddr)
	ret0, _ := ret[0].(netinterface.UDPConnInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DialUDP indicates an expected call of DialUDP.
func (mr *MockNetterUDPMockRecorder) DialUDP(network, laddr, raddr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DialUDP", reflect.TypeOf((*MockNetterUDP)(nil).DialUDP), network, laddr, raddr)
}

// ListenUDP mocks base method.
func (m *MockNetterUDP) ListenUDP(network string, laddr *net.UDPAddr) (netinterface.UDPConnInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListenUDP", network, laddr)
	ret0, _ := ret[0].(netinterface.UDPConnInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListenUDP indicates an expected call of ListenUDP.
func (mr *MockNetterUDPMockRecorder) ListenUDP(network, laddr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListenUDP", reflect.TypeOf((*MockNetterUDP)(nil).ListenUDP), network, laddr)
}

// ResolveUDPAddr mocks base method.
func (m *MockNetterUDP) ResolveUDPAddr(network, address string) (*net.UDPAddr, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveUDPAddr", network, address)
	ret0, _ := ret[0].(*net.UDPAddr)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveUDPAddr indicates an expected call of ResolveUDPAddr.
func (mr *MockNetterUDPMockRecorder) ResolveUDPAddr(network, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveUDPAddr", reflect.TypeOf((*MockNetterUDP)(nil).ResolveUDPAddr), network, address)
}

// MockUDPConnInterface is a mock of UDPConnInterface interface.
type MockUDPConnInterface struct {
	ctrl     *gomock.Controller
	recorder *MockUDPConnInterfaceMockRecorder
	isgomock struct{}
}

// MockUDPConnInterfaceMockRecorder is the mock recorder for MockUDPConnInterface.
type MockUDPConnInterfaceMockRecorder struct {
	mock *MockUDPConnInterface
}

// NewMockUDPConnInterface creates a new mock instance.
func NewMockUDPConnInterface(ctrl *gomock.Controller) *MockUDPConnInterface {
	mock := &MockUDPConnInterface{ctrl: ctrl}
	mock.recorder = &MockUDPConnInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUDPConnInterface) EXPECT() *MockUDPConnInterfaceMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockUDPConnInterface) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockUDPConnInterfaceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockUDPConnInterface)(nil).Close))
}

// File mocks base method.
func (m *MockUDPConnInterface) File() (*os.File, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "File")
	ret0, _ := ret[0].(*os.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// File indicates an expected call of File.
func (mr *MockUDPConnInterfaceMockRecorder) File() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "File", reflect.TypeOf((*MockUDPConnInterface)(nil).File))
}

// LocalAddr mocks base method.
func (m *MockUDPConnInterface) LocalAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocalAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// LocalAddr indicates an expected call of LocalAddr.
func (mr *MockUDPConnInterfaceMockRecorder) LocalAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocalAddr", reflect.TypeOf((*MockUDPConnInterface)(nil).LocalAddr))
}

// Read mocks base method.
func (m *MockUDPConnInterface) Read(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read.
func (mr *MockUDPConnInterfaceMockRecorder) Read(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockUDPConnInterface)(nil).Read), b)
}

// ReadFrom mocks base method.
func (m *MockUDPConnInterface) ReadFrom(b []byte) (int, net.Addr, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFrom", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(net.Addr)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadFrom indicates an expected call of ReadFrom.
func (mr *MockUDPConnInterfaceMockRecorder) ReadFrom(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFrom", reflect.TypeOf((*MockUDPConnInterface)(nil).ReadFrom), b)
}

// ReadFromUDP mocks base method.
func (m *MockUDPConnInterface) ReadFromUDP(b []byte) (int, *net.UDPAddr, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFromUDP", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*net.UDPAddr)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadFromUDP indicates an expected call of ReadFromUDP.
func (mr *MockUDPConnInterfaceMockRecorder) ReadFromUDP(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFromUDP", reflect.TypeOf((*MockUDPConnInterface)(nil).ReadFromUDP), b)
}

// ReadFromUDPAddrPort mocks base method.
func (m *MockUDPConnInterface) ReadFromUDPAddrPort(b []byte) (int, netip.AddrPort, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadFromUDPAddrPort", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(netip.AddrPort)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadFromUDPAddrPort indicates an expected call of ReadFromUDPAddrPort.
func (mr *MockUDPConnInterfaceMockRecorder) ReadFromUDPAddrPort(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadFromUDPAddrPort", reflect.TypeOf((*MockUDPConnInterface)(nil).ReadFromUDPAddrPort), b)
}

// ReadMsgUDP mocks base method.
func (m *MockUDPConnInterface) ReadMsgUDP(b, oob []byte) (int, int, int, *net.UDPAddr, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadMsgUDP", b, oob)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(int)
	ret3, _ := ret[3].(*net.UDPAddr)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// ReadMsgUDP indicates an expected call of ReadMsgUDP.
func (mr *MockUDPConnInterfaceMockRecorder) ReadMsgUDP(b, oob any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadMsgUDP", reflect.TypeOf((*MockUDPConnInterface)(nil).ReadMsgUDP), b, oob)
}

// ReadMsgUDPAddrPort mocks base method.
func (m *MockUDPConnInterface) ReadMsgUDPAddrPort(b, oob []byte) (int, int, int, netip.AddrPort, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadMsgUDPAddrPort", b, oob)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(int)
	ret3, _ := ret[3].(netip.AddrPort)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// ReadMsgUDPAddrPort indicates an expected call of ReadMsgUDPAddrPort.
func (mr *MockUDPConnInterfaceMockRecorder) ReadMsgUDPAddrPort(b, oob any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadMsgUDPAddrPort", reflect.TypeOf((*MockUDPConnInterface)(nil).ReadMsgUDPAddrPort), b, oob)
}

// RemoteAddr mocks base method.
func (m *MockUDPConnInterface) RemoteAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoteAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// RemoteAddr indicates an expected call of RemoteAddr.
func (mr *MockUDPConnInterfaceMockRecorder) RemoteAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoteAddr", reflect.TypeOf((*MockUDPConnInterface)(nil).RemoteAddr))
}

// SetDeadline mocks base method.
func (m *MockUDPConnInterface) SetDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeadline indicates an expected call of SetDeadline.
func (mr *MockUDPConnInterfaceMockRecorder) SetDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeadline", reflect.TypeOf((*MockUDPConnInterface)(nil).SetDeadline), t)
}

// SetReadBuffer mocks base method.
func (m *MockUDPConnInterface) SetReadBuffer(bytes int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadBuffer", bytes)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadBuffer indicates an expected call of SetReadBuffer.
func (mr *MockUDPConnInterfaceMockRecorder) SetReadBuffer(bytes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadBuffer", reflect.TypeOf((*MockUDPConnInterface)(nil).SetReadBuffer), bytes)
}

// SetReadDeadline mocks base method.
func (m *MockUDPConnInterface) SetReadDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadDeadline indicates an expected call of SetReadDeadline.
func (mr *MockUDPConnInterfaceMockRecorder) SetReadDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadDeadline", reflect.TypeOf((*MockUDPConnInterface)(nil).SetReadDeadline), t)
}

// SetWriteBuffer mocks base method.
func (m *MockUDPConnInterface) SetWriteBuffer(bytes int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWriteBuffer", bytes)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWriteBuffer indicates an expected call of SetWriteBuffer.
func (mr *MockUDPConnInterfaceMockRecorder) SetWriteBuffer(bytes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteBuffer", reflect.TypeOf((*MockUDPConnInterface)(nil).SetWriteBuffer), bytes)
}

// SetWriteDeadline mocks base method.
func (m *MockUDPConnInterface) SetWriteDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWriteDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWriteDeadline indicates an expected call of SetWriteDeadline.
func (mr *MockUDPConnInterfaceMockRecorder) SetWriteDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteDeadline", reflect.TypeOf((*MockUDPConnInterface)(nil).SetWriteDeadline), t)
}

// SyscallConn mocks base method.
func (m *MockUDPConnInterface) SyscallConn() (syscall.RawConn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyscallConn")
	ret0, _ := ret[0].(syscall.RawConn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyscallConn indicates an expected call of SyscallConn.
func (mr *MockUDPConnInterfaceMockRecorder) SyscallConn() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyscallConn", reflect.TypeOf((*MockUDPConnInterface)(nil).SyscallConn))
}

// Write mocks base method.
func (m *MockUDPConnInterface) Write(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockUDPConnInterfaceMockRecorder) Write(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockUDPConnInterface)(nil).Write), b)
}

// WriteMsgUDP mocks base method.
func (m *MockUDPConnInterface) WriteMsgUDP(b, oob []byte, addr *net.UDPAddr) (int, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMsgUDP", b, oob, addr)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// WriteMsgUDP indicates an expected call of WriteMsgUDP.
func (mr *MockUDPConnInterfaceMockRecorder) WriteMsgUDP(b, oob, addr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMsgUDP", reflect.TypeOf((*MockUDPConnInterface)(nil).WriteMsgUDP), b, oob, addr)
}

// WriteMsgUDPAddrPort mocks base method.
func (m *MockUDPConnInterface) WriteMsgUDPAddrPort(b, oob []byte, addr netip.AddrPort) (int, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMsgUDPAddrPort", b, oob, addr)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// WriteMsgUDPAddrPort indicates an expected call of WriteMsgUDPAddrPort.
func (mr *MockUDPConnInterfaceMockRecorder) WriteMsgUDPAddrPort(b, oob, addr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMsgUDPAddrPort", reflect.TypeOf((*MockUDPConnInterface)(nil).WriteMsgUDPAddrPort), b, oob, addr)
}

// WriteTo mocks base method.
func (m *MockUDPConnInterface) WriteTo(b []byte, addr net.Addr) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteTo", b, addr)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WriteTo indicates an expected call of WriteTo.
func (mr *MockUDPConnInterfaceMockRecorder) WriteTo(b, addr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteTo", reflect.TypeOf((*MockUDPConnInterface)(nil).WriteTo), b, addr)
}

// WriteToUDP mocks base method.
func (m *MockUDPConnInterface) WriteToUDP(b []byte, addr *net.UDPAddr) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteToUDP", b, addr)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WriteToUDP indicates an expected call of WriteToUDP.
func (mr *MockUDPConnInterfaceMockRecorder) WriteToUDP(b, addr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteToUDP", reflect.TypeOf((*MockUDPConnInterface)(nil).WriteToUDP), b, addr)
}

// WriteToUDPAddrPort mocks base method.
func (m *MockUDPConnInterface) WriteToUDPAddrPort(b []byte, addr netip.AddrPort) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteToUDPAddrPort", b, addr)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WriteToUDPAddrPort indicates an expected call of WriteToUDPAddrPort.
func (mr *MockUDPConnInterfaceMockRecorder) WriteToUDPAddrPort(b, addr any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteToUDPAddrPort", reflect.TypeOf((*MockUDPConnInterface)(nil).WriteToUDPAddrPort), b, addr)
}
