// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/command.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/command.go -destination=pkg/services/mock_services/command.go
//

// Package mock_services is a generated GoMock package.
package mock_services

import (
	tls "crypto/tls"
	reflect "reflect"

	logger "github.com/ansible/receptor/pkg/logger"
	netceptor "github.com/ansible/receptor/pkg/netceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockNetCForCommandService is a mock of NetCForCommandService interface.
type MockNetCForCommandService struct {
	ctrl     *gomock.Controller
	recorder *MockNetCForCommandServiceMockRecorder
	isgomock struct{}
}

// MockNetCForCommandServiceMockRecorder is the mock recorder for MockNetCForCommandService.
type MockNetCForCommandServiceMockRecorder struct {
	mock *MockNetCForCommandService
}

// NewMockNetCForCommandService creates a new mock instance.
func NewMockNetCForCommandService(ctrl *gomock.Controller) *MockNetCForCommandService {
	mock := &MockNetCForCommandService{ctrl: ctrl}
	mock.recorder = &MockNetCForCommandServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetCForCommandService) EXPECT() *MockNetCForCommandServiceMockRecorder {
	return m.recorder
}

// GetLogger mocks base method.
func (m *MockNetCForCommandService) GetLogger() *logger.ReceptorLogger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogger")
	ret0, _ := ret[0].(*logger.ReceptorLogger)
	return ret0
}

// GetLogger indicates an expected call of GetLogger.
func (mr *MockNetCForCommandServiceMockRecorder) GetLogger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogger", reflect.TypeOf((*MockNetCForCommandService)(nil).GetLogger))
}

// ListenAndAdvertise mocks base method.
func (m *MockNetCForCommandService) ListenAndAdvertise(service string, tlscfg *tls.Config, tags map[string]string) (*netceptor.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListenAndAdvertise", service, tlscfg, tags)
	ret0, _ := ret[0].(*netceptor.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListenAndAdvertise indicates an expected call of ListenAndAdvertise.
func (mr *MockNetCForCommandServiceMockRecorder) ListenAndAdvertise(service, tlscfg, tags any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListenAndAdvertise", reflect.TypeOf((*MockNetCForCommandService)(nil).ListenAndAdvertise), service, tlscfg, tags)
}
