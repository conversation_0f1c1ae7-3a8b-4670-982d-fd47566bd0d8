// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/services/tcp_proxy.go
//
// Generated by this command:
//
//	mockgen -source=pkg/services/tcp_proxy.go -destination=pkg/services/mock_services/tcp_proxy.go
//

// Package mock_services is a generated GoMock package.
package mock_services

import (
	tls "crypto/tls"
	io "io"
	net "net"
	reflect "reflect"
	time "time"

	logger "github.com/ansible/receptor/pkg/logger"
	netceptor "github.com/ansible/receptor/pkg/netceptor"
	gomock "go.uber.org/mock/gomock"
)

// MockNetcForTCPProxy is a mock of NetcForTCPProxy interface.
type MockNetcForTCPProxy struct {
	ctrl     *gomock.Controller
	recorder *MockNetcForTCPProxyMockRecorder
	isgomock struct{}
}

// MockNetcForTCPProxyMockRecorder is the mock recorder for MockNetcForTCPProxy.
type MockNetcForTCPProxyMockRecorder struct {
	mock *MockNetcForTCPProxy
}

// NewMockNetcForTCPProxy creates a new mock instance.
func NewMockNetcForTCPProxy(ctrl *gomock.Controller) *MockNetcForTCPProxy {
	mock := &MockNetcForTCPProxy{ctrl: ctrl}
	mock.recorder = &MockNetcForTCPProxyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetcForTCPProxy) EXPECT() *MockNetcForTCPProxyMockRecorder {
	return m.recorder
}

// Dial mocks base method.
func (m *MockNetcForTCPProxy) Dial(node, service string, tlscfg *tls.Config) (*netceptor.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dial", node, service, tlscfg)
	ret0, _ := ret[0].(*netceptor.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dial indicates an expected call of Dial.
func (mr *MockNetcForTCPProxyMockRecorder) Dial(node, service, tlscfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dial", reflect.TypeOf((*MockNetcForTCPProxy)(nil).Dial), node, service, tlscfg)
}

// GetLogger mocks base method.
func (m *MockNetcForTCPProxy) GetLogger() *logger.ReceptorLogger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogger")
	ret0, _ := ret[0].(*logger.ReceptorLogger)
	return ret0
}

// GetLogger indicates an expected call of GetLogger.
func (mr *MockNetcForTCPProxyMockRecorder) GetLogger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogger", reflect.TypeOf((*MockNetcForTCPProxy)(nil).GetLogger))
}

// ListenAndAdvertise mocks base method.
func (m *MockNetcForTCPProxy) ListenAndAdvertise(service string, tlscfg *tls.Config, tags map[string]string) (*netceptor.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListenAndAdvertise", service, tlscfg, tags)
	ret0, _ := ret[0].(*netceptor.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListenAndAdvertise indicates an expected call of ListenAndAdvertise.
func (mr *MockNetcForTCPProxyMockRecorder) ListenAndAdvertise(service, tlscfg, tags any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListenAndAdvertise", reflect.TypeOf((*MockNetcForTCPProxy)(nil).ListenAndAdvertise), service, tlscfg, tags)
}

// MockNetLib is a mock of NetLib interface.
type MockNetLib struct {
	ctrl     *gomock.Controller
	recorder *MockNetLibMockRecorder
	isgomock struct{}
}

// MockNetLibMockRecorder is the mock recorder for MockNetLib.
type MockNetLibMockRecorder struct {
	mock *MockNetLib
}

// NewMockNetLib creates a new mock instance.
func NewMockNetLib(ctrl *gomock.Controller) *MockNetLib {
	mock := &MockNetLib{ctrl: ctrl}
	mock.recorder = &MockNetLibMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetLib) EXPECT() *MockNetLibMockRecorder {
	return m.recorder
}

// Dial mocks base method.
func (m *MockNetLib) Dial(network, address string) (net.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dial", network, address)
	ret0, _ := ret[0].(net.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dial indicates an expected call of Dial.
func (mr *MockNetLibMockRecorder) Dial(network, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dial", reflect.TypeOf((*MockNetLib)(nil).Dial), network, address)
}

// Listen mocks base method.
func (m *MockNetLib) Listen(network, address string) (net.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Listen", network, address)
	ret0, _ := ret[0].(net.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Listen indicates an expected call of Listen.
func (mr *MockNetLibMockRecorder) Listen(network, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Listen", reflect.TypeOf((*MockNetLib)(nil).Listen), network, address)
}

// MockTLSLib is a mock of TLSLib interface.
type MockTLSLib struct {
	ctrl     *gomock.Controller
	recorder *MockTLSLibMockRecorder
	isgomock struct{}
}

// MockTLSLibMockRecorder is the mock recorder for MockTLSLib.
type MockTLSLibMockRecorder struct {
	mock *MockTLSLib
}

// NewMockTLSLib creates a new mock instance.
func NewMockTLSLib(ctrl *gomock.Controller) *MockTLSLib {
	mock := &MockTLSLib{ctrl: ctrl}
	mock.recorder = &MockTLSLibMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTLSLib) EXPECT() *MockTLSLibMockRecorder {
	return m.recorder
}

// Dial mocks base method.
func (m *MockTLSLib) Dial(network, addr string, config *tls.Config) (*tls.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dial", network, addr, config)
	ret0, _ := ret[0].(*tls.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Dial indicates an expected call of Dial.
func (mr *MockTLSLibMockRecorder) Dial(network, addr, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dial", reflect.TypeOf((*MockTLSLib)(nil).Dial), network, addr, config)
}

// NewListener mocks base method.
func (m *MockTLSLib) NewListener(inner net.Listener, config *tls.Config) net.Listener {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewListener", inner, config)
	ret0, _ := ret[0].(net.Listener)
	return ret0
}

// NewListener indicates an expected call of NewListener.
func (mr *MockTLSLibMockRecorder) NewListener(inner, config any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewListener", reflect.TypeOf((*MockTLSLib)(nil).NewListener), inner, config)
}

// MockNetListenerTCP is a mock of NetListenerTCP interface.
type MockNetListenerTCP struct {
	ctrl     *gomock.Controller
	recorder *MockNetListenerTCPMockRecorder
	isgomock struct{}
}

// MockNetListenerTCPMockRecorder is the mock recorder for MockNetListenerTCP.
type MockNetListenerTCPMockRecorder struct {
	mock *MockNetListenerTCP
}

// NewMockNetListenerTCP creates a new mock instance.
func NewMockNetListenerTCP(ctrl *gomock.Controller) *MockNetListenerTCP {
	mock := &MockNetListenerTCP{ctrl: ctrl}
	mock.recorder = &MockNetListenerTCPMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetListenerTCP) EXPECT() *MockNetListenerTCPMockRecorder {
	return m.recorder
}

// Accept mocks base method.
func (m *MockNetListenerTCP) Accept() (net.Conn, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Accept")
	ret0, _ := ret[0].(net.Conn)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Accept indicates an expected call of Accept.
func (mr *MockNetListenerTCPMockRecorder) Accept() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Accept", reflect.TypeOf((*MockNetListenerTCP)(nil).Accept))
}

// Addr mocks base method.
func (m *MockNetListenerTCP) Addr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Addr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// Addr indicates an expected call of Addr.
func (mr *MockNetListenerTCPMockRecorder) Addr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Addr", reflect.TypeOf((*MockNetListenerTCP)(nil).Addr))
}

// Close mocks base method.
func (m *MockNetListenerTCP) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockNetListenerTCPMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockNetListenerTCP)(nil).Close))
}

// MockUtilsLib is a mock of UtilsLib interface.
type MockUtilsLib struct {
	ctrl     *gomock.Controller
	recorder *MockUtilsLibMockRecorder
	isgomock struct{}
}

// MockUtilsLibMockRecorder is the mock recorder for MockUtilsLib.
type MockUtilsLibMockRecorder struct {
	mock *MockUtilsLib
}

// NewMockUtilsLib creates a new mock instance.
func NewMockUtilsLib(ctrl *gomock.Controller) *MockUtilsLib {
	mock := &MockUtilsLib{ctrl: ctrl}
	mock.recorder = &MockUtilsLibMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUtilsLib) EXPECT() *MockUtilsLibMockRecorder {
	return m.recorder
}

// BridgeConns mocks base method.
func (m *MockUtilsLib) BridgeConns(c1 io.ReadWriteCloser, c1Name string, c2 io.ReadWriteCloser, c2Name string, logger *logger.ReceptorLogger) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "BridgeConns", c1, c1Name, c2, c2Name, logger)
}

// BridgeConns indicates an expected call of BridgeConns.
func (mr *MockUtilsLibMockRecorder) BridgeConns(c1, c1Name, c2, c2Name, logger any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BridgeConns", reflect.TypeOf((*MockUtilsLib)(nil).BridgeConns), c1, c1Name, c2, c2Name, logger)
}

// MockTCPConn is a mock of TCPConn interface.
type MockTCPConn struct {
	ctrl     *gomock.Controller
	recorder *MockTCPConnMockRecorder
	isgomock struct{}
}

// MockTCPConnMockRecorder is the mock recorder for MockTCPConn.
type MockTCPConnMockRecorder struct {
	mock *MockTCPConn
}

// NewMockTCPConn creates a new mock instance.
func NewMockTCPConn(ctrl *gomock.Controller) *MockTCPConn {
	mock := &MockTCPConn{ctrl: ctrl}
	mock.recorder = &MockTCPConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTCPConn) EXPECT() *MockTCPConnMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockTCPConn) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockTCPConnMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockTCPConn)(nil).Close))
}

// LocalAddr mocks base method.
func (m *MockTCPConn) LocalAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocalAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// LocalAddr indicates an expected call of LocalAddr.
func (mr *MockTCPConnMockRecorder) LocalAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocalAddr", reflect.TypeOf((*MockTCPConn)(nil).LocalAddr))
}

// Read mocks base method.
func (m *MockTCPConn) Read(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read.
func (mr *MockTCPConnMockRecorder) Read(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockTCPConn)(nil).Read), b)
}

// RemoteAddr mocks base method.
func (m *MockTCPConn) RemoteAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoteAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// RemoteAddr indicates an expected call of RemoteAddr.
func (mr *MockTCPConnMockRecorder) RemoteAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoteAddr", reflect.TypeOf((*MockTCPConn)(nil).RemoteAddr))
}

// SetDeadline mocks base method.
func (m *MockTCPConn) SetDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeadline indicates an expected call of SetDeadline.
func (mr *MockTCPConnMockRecorder) SetDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeadline", reflect.TypeOf((*MockTCPConn)(nil).SetDeadline), t)
}

// SetReadDeadline mocks base method.
func (m *MockTCPConn) SetReadDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadDeadline indicates an expected call of SetReadDeadline.
func (mr *MockTCPConnMockRecorder) SetReadDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadDeadline", reflect.TypeOf((*MockTCPConn)(nil).SetReadDeadline), t)
}

// SetWriteDeadline mocks base method.
func (m *MockTCPConn) SetWriteDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWriteDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWriteDeadline indicates an expected call of SetWriteDeadline.
func (mr *MockTCPConnMockRecorder) SetWriteDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteDeadline", reflect.TypeOf((*MockTCPConn)(nil).SetWriteDeadline), t)
}

// Write mocks base method.
func (m *MockTCPConn) Write(b []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", b)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockTCPConnMockRecorder) Write(b any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockTCPConn)(nil).Write), b)
}
