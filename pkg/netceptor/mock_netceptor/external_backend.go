// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/netceptor/external_backend.go
//
// Generated by this command:
//
//	mockgen -source=pkg/netceptor/external_backend.go -destination=pkg/netceptor/mock_netceptor/external_backend.go
//

// Package mock_netceptor is a generated GoMock package.
package mock_netceptor

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockMessageConn is a mock of MessageConn interface.
type MockMessageConn struct {
	ctrl     *gomock.Controller
	recorder *MockMessageConnMockRecorder
	isgomock struct{}
}

// MockMessageConnMockRecorder is the mock recorder for MockMessageConn.
type MockMessageConnMockRecorder struct {
	mock *MockMessageConn
}

// NewMockMessageConn creates a new mock instance.
func NewMockMessageConn(ctrl *gomock.Controller) *MockMessageConn {
	mock := &MockMessageConn{ctrl: ctrl}
	mock.recorder = &MockMessageConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMessageConn) EXPECT() *MockMessageConnMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockMessageConn) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockMessageConnMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockMessageConn)(nil).Close))
}

// ReadMessage mocks base method.
func (m *MockMessageConn) ReadMessage(ctx context.Context, timeout time.Duration) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadMessage", ctx, timeout)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadMessage indicates an expected call of ReadMessage.
func (mr *MockMessageConnMockRecorder) ReadMessage(ctx, timeout any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadMessage", reflect.TypeOf((*MockMessageConn)(nil).ReadMessage), ctx, timeout)
}

// SetReadDeadline mocks base method.
func (m *MockMessageConn) SetReadDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadDeadline indicates an expected call of SetReadDeadline.
func (mr *MockMessageConnMockRecorder) SetReadDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadDeadline", reflect.TypeOf((*MockMessageConn)(nil).SetReadDeadline), t)
}

// WriteMessage mocks base method.
func (m *MockMessageConn) WriteMessage(ctx context.Context, data []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMessage", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteMessage indicates an expected call of WriteMessage.
func (mr *MockMessageConnMockRecorder) WriteMessage(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMessage", reflect.TypeOf((*MockMessageConn)(nil).WriteMessage), ctx, data)
}
