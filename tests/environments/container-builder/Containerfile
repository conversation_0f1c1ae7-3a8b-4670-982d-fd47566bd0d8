FROM quay.io/centos/centos:stream9

# Python options = [3.9, 3.11, 3.12]
ARG PYTHON_VERSION=3.12

ENV PATH=${PATH}:/usr/local/go/bin

RUN set -x \
    && echo 'fastestmirror=True' >> /etc/dnf/dnf.conf \
    && dnf update -y \
    # Receptor build tools
    && dnf install -y \
        findutils \
        git \
        iproute \
        make \
        openssl \
        wget  \
    # Install specific python version
    && dnf install -y \
        python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-pip \
    && pip${PYTHON_VERSION} install virtualenv \
    # Install specific golang version
    && dnf install -y \
        golang \
    && dnf clean all

# --- ALL IMAGE MUST BE THE SAME UNTIL NOW ---

# Caching dependencies
WORKDIR /dependencies
ADD ./go.mod \
    ./go.sum \
    ../receptorctl/requirements/requirements.txt ./
RUN set -x \
    # Go
    && go get -u golang.org/x/lint/golint \
    && go get -d -v ./... \
    # Python
    && virtualenv -p python${PYTHON_VERSION} /opt/venv \
    && source /opt/venv/bin/activate \
    && pip${PYTHON_VERSION} install \
        --upgrade \
        -r requirements.txt

ADD ./tests/environments/container-builder/build-artifacts.sh /

RUN chmod +x /build-artifacts.sh

WORKDIR /
