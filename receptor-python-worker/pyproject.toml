[project]
name = "receptor-python-worker"
authors = [{name = "Red Hat", email = "<EMAIL>"}]
description = "The receptor-python-worker command is called by <PERSON><PERSON> to supervise the operation of a Python worker plugin."
readme = "README.md"
license = "Apache-2.0"
dynamic = ["version"]

[build-system]
requires = ["setuptools", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[tool.setuptools_scm]
fallback_version = "0.0.0"

[project.scripts]
receptor-python-worker = "receptor_python_worker:run"