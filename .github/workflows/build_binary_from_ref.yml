---
name: "Build binary from arbitratry repo / ref"
on:  # yamllint disable-line rule:truthy
  workflow_dispatch:
    inputs:
      repository:
        description: 'The receptor repository to build from.'
        required: true
        default: 'ansible/receptor'
      ref:
        description: 'The ref to build. Can be a branch or any other valid ref.'
        required: true
        default: 'devel'
jobs:
  build:
    name: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          repository: ${{ github.event.inputs.repository }}
          ref: ${{ github.event.inputs.ref }}

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23"

      - name: build-all target
        run: make receptor

      - name: Upload binary
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          pip install boto3
          ansible -i localhost, -c local all -m aws_s3 \
            -a "bucket=receptor-nightlies object=refs/${{ github.event.inputs.ref }}/receptor src=./receptor mode=put"
