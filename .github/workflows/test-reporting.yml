---
name: Generate junit test report

on:  # yamllint disable-line rule:truthy
  pull_request:  # yamllint disable-line rule:empty-values
  push:
    branches: [devel]

env:
  DESIRED_GO_VERSION: '1.23'

jobs:
  generate_junit_test_report:
    name: go test coverage
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.DESIRED_GO_VERSION }}

      - name: build and install receptor
        run: |
          make build-all
          sudo cp ./receptor /usr/local/bin/receptor

      - name: Download kind binary
        run: curl -Lo ./kind https://kind.sigs.k8s.io/dl/latest/kind-linux-amd64 && chmod +x ./kind

      - name: Create k8s cluster
        run: ./kind create cluster

      - name: Interact with the cluster
        run: kubectl get nodes

      - name: Install go junit reporting
        run: go install github.com/jstemmer/go-junit-report/v2@latest

      - name: Run receptor tests
        run: go test -v 2>&1 $(go list ./... | grep -vE '/tests/|mock_|example') | go-junit-report > report.xml

      - name: Upload test results to dashboard
        if: >-
          !cancelled()
          && github.event_name == 'push'
          && github.repository == 'ansible/receptor'
          && github.ref_name == github.event.repository.default_branch
        run: >-
          curl -v --user "${{ vars.PDE_ORG_RESULTS_AGGREGATOR_UPLOAD_USER }}:${{ secrets.PDE_ORG_RESULTS_UPLOAD_PASSWORD }}"
          --form "xunit_xml=@report.xml"
          --form "component_name=receptor"
          --form "git_commit_sha=${{ github.sha }}"
          --form "git_repository_url=https://github.com/${{ github.repository }}"
          "${{ vars.PDE_ORG_RESULTS_AGGREGATOR_UPLOAD_URL }}/api/results/upload/"

      - name: get k8s logs
        if: ${{ failure() }}
        run: .github/workflows/artifact-k8s-logs.sh

      - name: remove sockets before archiving logs
        if: ${{ failure() }}
        run: find /tmp/receptor-testing -name controlsock -delete

      - name: Artifact receptor data
        uses: actions/upload-artifact@v4
        if: ${{ failure() }}
        with:
          name: test-logs
          path: /tmp/receptor-testing

      - name: Archive receptor binary
        uses: actions/upload-artifact@v4
        with:
          name: receptor
          path: /usr/local/bin/receptor
