---
name: Triage

on:  # yamllint disable-line rule:truthy
  issues:
    types:
      - opened

jobs:
  triage:
    runs-on: ubuntu-latest
    name: Label

    steps:
      - name: Label issues
        uses: github/issue-labeler@v3.4
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          not-before: 2021-12-07T07:00:00Z
          configuration-path: .github/issue_labeler.yml
          enable-versioned-regex: 0
        if: github.event_name == 'issues'
