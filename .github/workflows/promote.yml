---
name: Promote Release
on:  # yamllint disable-line rule:truthy
  release:
    types: [published]

permissions:
  contents: write

jobs:
  promote:
    runs-on: ubuntu-latest
    env:
      TAG: ${{github.event.release.tag_name}}
    steps:
      - name: Checkout Receptor
        uses: actions/checkout@v4

      - name: Log in to GHCR
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Log in to Quay
        run: |
          echo ${{ secrets.QUAY_TOKEN }} | docker login quay.io -u ${{ secrets.QUAY_USER }} --password-stdin

      - name: Copy Image to Quay
        uses: akhilerm/tag-push-action@v2.2.0
        with:
          src: ghcr.io/${{ github.repository }}:${{env.TAG}}
          dst: |
            quay.io/${{ github.repository }}:${{env.TAG}}
            quay.io/${{ github.repository }}:latest

      - name: Check if floating tag is needed
        run: |
          if [[ $TAG == *"dev"* ]];
          then
           echo "FLOATING_TAG=$(echo $TAG | sed 's/[0-9]\+$//')" >> $GITHUB_ENV
          else
           echo "FLOATING_TAG=$TAG" >> $GITHUB_ENV
          fi

      - name: Push floating tag to Quay
        uses: akhilerm/tag-push-action@v2.2.0
        with:
          src: ghcr.io/${{ github.repository }}:${{env.TAG}}
          dst: quay.io/${{ github.repository }}:${{env.FLOATING_TAG}}

      - name: Install python
        uses: actions/setup-python@v5

      - name: Install dependencies
        run: |
          python3 -m pip install twine build

      - name: Set official pypi info
        run: echo pypi_repo=pypi >> $GITHUB_ENV
        if: ${{ github.repository_owner == 'ansible' }}

      - name: Set unofficial pypi info
        run: echo pypi_repo=testpypi >> $GITHUB_ENV
        if: ${{ github.repository_owner != 'ansible' }}

      - name: Set receptor pypi version
        run: echo RECEPTORCTL_PYPI_VERSION=$(curl --silent https://pypi.org/pypi/receptorctl/json | jq --raw-output '"v" + .info.version') >> $GITHUB_ENV

      - name: Build receptorctl and upload to pypi
        if: ${{ env.RECEPTORCTL_PYPI_VERSION != env.TAG }}
        run: |
          make receptorctl_wheel receptorctl_sdist VERSION=$TAG
          twine upload \
            -r ${{ env.pypi_repo }} \
            -u ${{ secrets.PYPI_USERNAME }} \
            -p ${{ secrets.PYPI_PASSWORD }} \
            receptorctl/dist/*

  publish:
    runs-on: ubuntu-latest
    env:
      VERSION: ${{github.event.release.tag_name}}
    steps:
      - name: Checkout Receptor
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23"

      - name: Build packages
        run: |
          make build-package GOOS=linux GOARCH=amd64 BINNAME=receptor
          make build-package GOOS=linux GOARCH=arm64 BINNAME=receptor
          make build-package GOOS=darwin GOARCH=amd64 BINNAME=receptor
          make build-package GOOS=darwin GOARCH=arm64 BINNAME=receptor
          make build-package GOOS=windows GOARCH=amd64 BINNAME=receptor.exe
          make build-package GOOS=windows GOARCH=arm64 BINNAME=receptor.exe

      - name: Publish packages
        uses: softprops/action-gh-release@v2
        with:
          files: |-
            dist/checksums.txt
            dist/*.tar.gz
