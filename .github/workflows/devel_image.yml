---

name: Publish devel image

on:  # yamllint disable-line rule:truthy
  push:
    branches: [devel]

jobs:
  release:
    runs-on: ubuntu-latest
    name: Push devel image
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # setup qemu and buildx
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Install build dependencies
        run: |
          pip install build

       # we will first build the image for x86 and load it on the host for testing
      - name: Build Image
        run: |
          export CONTAINERCMD="docker buildx"
          export EXTRA_OPTS="--platform linux/amd64 --load"
          make container REPO=quay.io/${{ github.repository }} TAG=devel

      - name: Test Image
        run: docker run --rm quay.io/${{ github.repository }}:devel receptor --version

      - name: Login To Quay
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.QUAY_USERNAME }}
          password: ${{ secrets.QUAY_TOKEN }}
          registry: quay.io/${{ github.repository }}

      # Since x86 image is built in previous step
      # buildx will use cached image, hence overall time will not be affected
      - name: Build Multiarch Image & Push To Quay
        run: |
          export  CONTAINERCMD="docker buildx"
          export EXTRA_OPTS="--platform linux/amd64,linux/ppc64le,linux/arm64 --push"
          make container REPO=quay.io/${{ github.repository }} TAG=devel
