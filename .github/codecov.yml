---
coverage:
  status:
    project:
      default:
        target: auto
        threshold: 5%
    patch:
      default:
        target: auto
        threshold: 5%
comment:
  layout: "header, diff, files, components"  # PR comment layout
  behavior: default
  require_changes: false
  require_base: false
  require_head: true
  after_n_builds: 1
codecov:
  branch: devel
  notify:
    wait_for_ci: false
    after_n_builds: 1
component_management:
  default_rules:  # default rules that will be inherited by all components
    statuses:   # Status definitions
      - type: project  # Default status = project
        target: auto
        threshold: 5%
        branches:
          - "!devel"
  individual_components:   # Actual component breakdown
    - component_id: go  # Required unique identifier for component (Should not be changed)
      name: Go  # Display name (can be changed)
      paths:
        - cmd/**
        - internal/**
        - pkg/**
    - component_id: receptorctl
      name: Receptorctl
      paths:
        - receptorctl/**
ignore:
  - "**/mock_*"
  - "**/*_test.go"
  - "receptorctl/tests"
  - "tests"
