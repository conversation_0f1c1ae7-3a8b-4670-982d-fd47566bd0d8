---
run:
  timeout: 10m

linters:
  disable-all: true
  enable:
    - asciicheck
    - bodyclose
    - depguard
    - dogsled
    - durationcheck
    - exportloopref
    - gci
    - gocritic
    - godot
    - gofmt
    - gofumpt
    - goheader
    - goimports
    - gomodguard
    - gosec
    - gosimple
    - govet
    - importas
    - ineffassign
    - makezero
    - misspell
    - nakedret
    - nilerr
    - nlreturn
    - noctx
    - nolintlint
    - prealloc
    - predeclared
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - stylecheck
    - tparallel
    - typecheck
    - unconvert
    - unused
    - wastedassign
    - whitespace

linters-settings:
  depguard:
    rules:
      main:
        files:
          - "$all"
          - "!$test"
          - "!**/functional/**/*.go"
        allow:
          - "$gostd"
          - "github.com/ansible/receptor/internal/version"
          - "github.com/ansible/receptor/cmd"
          - "github.com/ansible/receptor/pkg"
          - "github.com/creack/pty"
          - "github.com/fsnotify/fsnotify"
          - "github.com/ghjm/cmdline"
          - "github.com/golang-jwt/jwt/v4"
          - "github.com/google/shlex"
          - "github.com/gorilla/websocket"
          - "github.com/jupp0r/go-priority-queue"
          - "github.com/minio/highwayhash"
          - "github.com/pbnjay/memory"
          - "github.com/quic-go/quic-go"
          - "github.com/rogpeppe/go-internal/lockedfile"
          - "github.com/songgao/water"
          - "github.com/vishvananda/netlink"
          - "github.com/spf13/viper"
          - "github.com/spf13/cobra"
          - "k8s.io/api/core"
          - "k8s.io/apimachinery/pkg"
          - "k8s.io/client-go"
          - "github.com/grafana/pyroscope-go"
          - "github.com/sirupsen/logrus"
      tests:
        files:
          - "$test"
          - "**/functional/**/*.go"
        allow:
          - "$gostd"
          - "github.com/ansible/receptor/pkg"
          - "github.com/ansible/receptor/tests/utils"
          - "github.com/fortytw2/leaktest"
          - "github.com/fsnotify/fsnotify"
          - "github.com/gorilla/websocket"
          - "github.com/prep/socketpair"
          - "github.com/google/go-cmp/cmp"
          - "k8s.io/api/core/v1"
          - "k8s.io/apimachinery/pkg/api/errors"
          - "k8s.io/apimachinery/pkg/apis/meta/v1"
          - "k8s.io/apimachinery/pkg/fields"
          - "k8s.io/apimachinery/pkg/selection"
          - "k8s.io/apimachinery/pkg/watch"
          - "k8s.io/client-go/kubernetes"
          - "k8s.io/client-go/rest"
          - "k8s.io/client-go/tools/remotecommand"
          - "github.com/quic-go/quic-go"
          - "github.com/quic-go/quic-go/logging"
          - "github.com/AaronH88/quic-go"
          - "github.com/stretchr/testify/assert"

issues:
  # Dont commit the following line.
  # It will make CI pass without telling you about errors.
  # fix: true
  exclude:
    - "lostcancel"  # TODO: Context is not canceled on multiple occasions. Needs more detailed work to be fixed.
    - "SA2002|thelper|testinggoroutine"  # TODO: Test interface used outside of its routine, tests need to be rewritten.
    - "G306"  # TODO: Restrict perms of touched files.
    - "G402|G404"  # TODO: Make TLS more secure.
    - "G204"  # gosec is throwing a fit, ignore.
...
