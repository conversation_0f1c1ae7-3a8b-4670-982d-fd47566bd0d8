=================
Receptor Roadmaps
=================

The ``Receptor`` team develops a roadmap for each major Receptor release.
The latest roadmap shows current work; older roadmaps provide a history of the
project.  We don't publish roadmaps for minor or subminor versions. So 2.0 and
3.0 have roadmaps, but 2.1.0 and 2.10.1 do not.

We incorporate team and community feedback in each roadmap, and aim for further
transparency and better inclusion of both community desires and submissions.

Each roadmap offers a *best guess*, based on the ``Receptor`` team's experience
and on requests and feedback from the community, of what will be included in a
given release.  However, some items on the roadmap may be dropped due to time
constraints, lack of community maintainers, and so on.

Each roadmap is published both as an idea of what is upcoming in ``Receptor``,
and as a medium for seeking further feedback from the community.

You can submit feedback on the current roadmap by:
- Creating issue on GitHub in `ansible/receptor repository <https://github.com/ansible/receptor/>`_

Go to `Ansible forum <https://forum.ansible.com/>`_ to join the community discussions.

.. toctree::
   :maxdepth: 1
   :glob:
   :caption: Receptor Roadmaps

   ROADMAP_2
