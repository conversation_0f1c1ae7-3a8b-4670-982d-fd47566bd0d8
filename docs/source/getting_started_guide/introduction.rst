########################
Introduction to receptor
########################

Receptor is an overlay network.
It eases the work distribution across a large and dispersed collection
of workers

Receptor nodes establish peer-to-peer connections with each other through
existing networks

Once connected, the receptor mesh provides:

* Datagram (UDP-like) and stream (TCP-like) capabilities to applications
* Robust unit-of-work handling
* Resiliency against transient network failures
